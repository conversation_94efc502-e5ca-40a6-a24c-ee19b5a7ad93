/**
 * WebSocket连接管理
 * 负责视频流和警报的WebSocket连接
 */

// WebSocket连接管理器
class WebSocketManager {
    constructor() {
        this.videoSocket = null;
        this.alertSocket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 3000;
        this.isConnecting = false;
    }

    /**
     * 初始化WebSocket连接
     */
    initialize() {
        this.connectVideoStream();
        this.connectAlertStream();
    }

    /**
     * 连接视频流WebSocket
     */
    connectVideoStream() {
        if (this.isConnecting) return;
        
        this.isConnecting = true;
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/video`;

        console.log('连接视频流WebSocket:', wsUrl);

        try {
            this.videoSocket = new WebSocket(wsUrl);
            
            this.videoSocket.onopen = () => {
                console.log('视频流WebSocket连接成功');
                this.reconnectAttempts = 0;
                this.isConnecting = false;
                this.updateVideoStatus('connected');
            };

            this.videoSocket.onmessage = (event) => {
                this.handleVideoMessage(event);
            };

            this.videoSocket.onclose = (event) => {
                console.log('视频流WebSocket连接关闭:', event.code, event.reason);
                this.isConnecting = false;
                this.updateVideoStatus('disconnected');
                this.scheduleVideoReconnect();
            };

            this.videoSocket.onerror = (error) => {
                console.error('视频流WebSocket错误:', error);
                this.isConnecting = false;
                this.updateVideoStatus('error');
            };

        } catch (error) {
            console.error('创建视频流WebSocket失败:', error);
            this.isConnecting = false;
            this.updateVideoStatus('error');
        }
    }

    /**
     * 连接警报WebSocket
     */
    connectAlertStream() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/alerts`;

        console.log('连接警报WebSocket:', wsUrl);

        try {
            this.alertSocket = new WebSocket(wsUrl);
            
            this.alertSocket.onopen = () => {
                console.log('警报WebSocket连接成功');
            };

            this.alertSocket.onmessage = (event) => {
                this.handleAlertMessage(event);
            };

            this.alertSocket.onclose = (event) => {
                console.log('警报WebSocket连接关闭:', event.code, event.reason);
                this.scheduleAlertReconnect();
            };

            this.alertSocket.onerror = (error) => {
                console.error('警报WebSocket错误:', error);
            };

        } catch (error) {
            console.error('创建警报WebSocket失败:', error);
        }
    }

    /**
     * 处理视频消息
     */
    handleVideoMessage(event) {
        try {
            if (event.data instanceof Blob) {
                // 处理二进制视频数据
                const url = URL.createObjectURL(event.data);
                const videoFeed = document.getElementById('videoFeed');
                if (videoFeed) {
                    videoFeed.src = url;
                    // 清理旧的URL对象
                    if (videoFeed.previousSrc) {
                        URL.revokeObjectURL(videoFeed.previousSrc);
                    }
                    videoFeed.previousSrc = url;
                }
            } else {
                // 处理JSON消息
                const data = JSON.parse(event.data);
                this.handleVideoData(data);
            }
        } catch (error) {
            console.error('处理视频消息失败:', error);
        }
    }

    /**
     * 处理视频数据
     */
    handleVideoData(data) {
        if (data.type === 'frame') {
            // 处理视频帧
            const videoFeed = document.getElementById('videoFeed');
            if (videoFeed && data.image) {
                videoFeed.src = `data:image/jpeg;base64,${data.image}`;
            }
        } else if (data.type === 'status') {
            // 处理状态更新
            this.updateVideoStatus(data.status);
        }
    }

    /**
     * 处理警报消息
     */
    handleAlertMessage(event) {
        try {
            const alertData = JSON.parse(event.data);
            console.log('收到警报:', alertData);
            
            // 触发警报处理事件
            const alertEvent = new CustomEvent('newAlert', {
                detail: alertData
            });
            document.dispatchEvent(alertEvent);
            
        } catch (error) {
            console.error('处理警报消息失败:', error);
        }
    }

    /**
     * 更新视频状态
     */
    updateVideoStatus(status) {
        const statusElement = document.getElementById('videoStatus');
        const sourceDisplay = document.getElementById('videoSourceDisplay');
        
        if (statusElement) {
            switch (status) {
                case 'connected':
                    statusElement.className = 'inline-block w-3 h-3 rounded-full bg-green-500 mr-2';
                    if (sourceDisplay) sourceDisplay.textContent = '已连接';
                    break;
                case 'disconnected':
                    statusElement.className = 'inline-block w-3 h-3 rounded-full bg-red-500 mr-2';
                    if (sourceDisplay) sourceDisplay.textContent = '未连接';
                    break;
                case 'error':
                    statusElement.className = 'inline-block w-3 h-3 rounded-full bg-yellow-500 mr-2';
                    if (sourceDisplay) sourceDisplay.textContent = '连接错误';
                    break;
                default:
                    statusElement.className = 'inline-block w-3 h-3 rounded-full bg-gray-500 mr-2';
                    if (sourceDisplay) sourceDisplay.textContent = '未知状态';
            }
        }
    }

    /**
     * 安排视频流重连
     */
    scheduleVideoReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('视频流重连次数已达上限');
            return;
        }

        this.reconnectAttempts++;
        console.log(`${this.reconnectDelay / 1000}秒后尝试重连视频流 (第${this.reconnectAttempts}次)`);

        setTimeout(() => {
            if (!this.videoSocket || this.videoSocket.readyState === WebSocket.CLOSED) {
                this.connectVideoStream();
            }
        }, this.reconnectDelay);

        // 递增重连延迟
        this.reconnectDelay = Math.min(this.reconnectDelay * 1.5, 30000);
    }

    /**
     * 安排警报流重连
     */
    scheduleAlertReconnect() {
        setTimeout(() => {
            if (!this.alertSocket || this.alertSocket.readyState === WebSocket.CLOSED) {
                this.connectAlertStream();
            }
        }, 3000);
    }

    /**
     * 关闭所有连接
     */
    disconnect() {
        if (this.videoSocket) {
            this.videoSocket.close();
            this.videoSocket = null;
        }
        
        if (this.alertSocket) {
            this.alertSocket.close();
            this.alertSocket = null;
        }
    }

    /**
     * 重新连接所有WebSocket
     */
    reconnectAll() {
        this.disconnect();
        setTimeout(() => {
            this.initialize();
        }, 1000);
    }
}

// 创建全局WebSocket管理器实例
window.wsManager = new WebSocketManager();

// 页面加载完成后初始化WebSocket连接
document.addEventListener('DOMContentLoaded', function() {
    console.log('初始化WebSocket连接...');
    window.wsManager.initialize();
});

// 页面卸载时关闭连接
window.addEventListener('beforeunload', function() {
    if (window.wsManager) {
        window.wsManager.disconnect();
    }
});

// 网络状态变化时重连
window.addEventListener('online', function() {
    console.log('网络已连接，重新连接WebSocket...');
    if (window.wsManager) {
        window.wsManager.reconnectAll();
    }
});

window.addEventListener('offline', function() {
    console.log('网络已断开');
});
