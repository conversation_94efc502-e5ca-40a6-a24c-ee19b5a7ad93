# -*- coding: utf-8 -*-
"""
多模态视频分析模块
功能：结合视觉和文本分析监控视频内容，检测异常事件
作者：18523
创建时间：2025年2月17日
"""

# 导入基础库
import base64       # 用于Base64编码解码
import requests     # HTTP请求库
import cv2          # OpenCV计算机视觉库
import time         # 时间处理模块
import numpy as np  # 数值计算库
import json         # JSON数据处理
import httpx        # 异步HTTP客户端
import os           # 操作系统接口
import datetime     # 日期时间处理
import asyncio      # 异步IO框架
import re
from zhconv import convert
# 从自定义模块导入功能
from utils.utility import video_chat_async, chat_request, insert_txt, video_chat_async_limit_frame
from src.core.config_adapter import RAGConfig, MODEL_NAMES  # 导入模型名称映射
from src.api.prompt import prompt_detect, prompt_summary, prompt_vieo  # 预设提示模板

class MultiModalAnalyzer:
    def __init__(self):
        # 初始化消息队列和时间线记录
        self.message_queue = []     # 存储历史分析结果
        self.time_step_story = []   # 时间线记录（当前代码中未使用）

    def generate_english_filename(self, alert_content, timestamp, current_model_name=None):
        """
        根据当前选择的模型生成英文文件名（不再基于推断）
        参数：
        - alert_content: 异常检测内容（保留兼容性）
        - timestamp: 时间戳
        - current_model_name: 当前选择的模型名称
        返回：英文文件名前缀
        """
        # 直接使用当前选择的模型名称，不再推断
        if current_model_name:
            # 如果传入了当前模型名称，直接使用
            if isinstance(current_model_name, list):
                # 如果是多模型，使用第一个
                english_name = current_model_name[0] if current_model_name else "general"
            else:
                english_name = current_model_name
        else:
            # 降级：如果没有传入模型名称，使用通用检测
            english_name = "general"
            print(f"⚠️ 未传入模型名称，使用默认: {english_name}")

        # 确保英文名称有效
        if english_name not in MODEL_NAMES:
            print(f"⚠️ 模型名称 '{english_name}' 不在MODEL_NAMES中，使用general")
            english_name = "general"

        print(f"📁 生成文件名: {english_name}_{timestamp} (基于当前模型: {english_name})")
        return f"{english_name}_{timestamp}"

    def infer_detection_type(self, alert_content):
        """
        根据异常内容推断检测类型
        参数：
        - alert_content: 异常检测内容
        返回：推断的检测类型（中文）
        """
        if not alert_content or alert_content == "无异常":
            return "通用异常检测"

        # 关键词映射，根据异常描述内容推断检测类型
        keyword_mapping = {
            # 安全防护类
            "安全帽": "安全帽检测",
            "头盔": "安全帽检测",
            "工作服": "工作服检测",
            "反光衣": "反光衣识别",
            "厨师帽": "厨师帽识别",
            "口罩": "口罩检测",  # 匹配MODEL_NAMES中的定义
            "面罩": "口罩检测",
            "防护面罩": "口罩检测",
            "绝缘鞋": "绝缘鞋检测",
            "安全鞋": "绝缘鞋检测",
            "防护鞋": "绝缘鞋检测",
            "运动鞋": "绝缘鞋检测",  # 在电力环境中，运动鞋通常不符合安全要求

            # 行为检测类
            "吸烟": "吸烟检测",
            "抽烟": "吸烟检测",
            "打电话": "打电话检测",
            "电话": "打电话检测",
            "打架": "打架斗殴检测",
            "斗殴": "打架斗殴检测",
            "冲突": "打架斗殴检测",
            "武器": "持械识别",
            "刀具": "持械识别",
            "睡觉": "睡岗检测",
            "睡岗": "睡岗检测",
            "离岗": "离岗检测",
            "脱岗": "离岗检测",

            # 区域监控类
            "入侵": "入侵检测",
            "闯入": "入侵检测",
            "周界": "周界入侵检测",
            "围栏": "电子围栏",
            "徘徊": "徘徊检测",

            # 计数统计类
            "人数": "计数检测",
            "计数": "计数检测",
            "越线": "越线计数",
            "超限": "人员超限检测",
            "拥挤": "人员超限检测",

            # 识别检测类
            "人脸": "人脸识别",
            "面部": "人脸识别",
            "车牌": "车牌识别",
            "陌生人": "陌生人告警",

            # 环境安全类
            "火焰": "火焰烟火检测",
            "烟火": "火焰烟火检测",
            "明火": "明火检测",
            "火": "明火检测",
            "烟尘": "烟尘检测",
            "消防通道": "消防通道阻塞识别",
            "通道阻塞": "消防通道阻塞识别",

            # 意外事件类
            "跌倒": "跌倒检测",
            "摔倒": "跌倒检测",
            "倒地": "跌倒检测",
        }

        # 遍历关键词映射，找到匹配的检测类型
        for keyword, detection_type in keyword_mapping.items():
            if keyword in alert_content:
                return detection_type

        # 如果没有匹配到特定类型，返回通用检测
        return "通用异常检测"

    def map_detection_type_to_storage(self, inferred_type, current_alert_names):
        """
        智能映射检测类型到存储格式
        参数：
        - inferred_type: 推断的检测类型（中文）
        - current_alert_names: 当前选择的模型名称
        返回：存储用的英文键名
        """
        # 1. 优先检查：如果推断类型与当前选择的模型匹配，直接使用当前模型
        if isinstance(current_alert_names, list):
            for model_name in current_alert_names:
                if model_name in MODEL_NAMES and MODEL_NAMES[model_name] == inferred_type:
                    print(f"  匹配当前选择的模型: {model_name}")
                    return model_name
        elif isinstance(current_alert_names, str):
            if current_alert_names in MODEL_NAMES and MODEL_NAMES[current_alert_names] == inferred_type:
                print(f"  匹配当前选择的模型: {current_alert_names}")
                return current_alert_names

        # 2. 标准映射：从MODEL_NAMES中查找匹配
        chinese_to_english = {v: k for k, v in MODEL_NAMES.items()}
        if inferred_type in chinese_to_english:
            matched_key = chinese_to_english[inferred_type]
            print(f"  标准映射匹配: {inferred_type} -> {matched_key}")
            return matched_key

        # 3. 模糊匹配：处理"检测"和"监测"的差异
        fuzzy_mapping = {
            "口罩监测": "mask",
            "口罩检测": "mask",
            "安全帽监测": "helmet",
            "安全帽检测": "helmet",
            "工作服监测": "uniform",
            "工作服检测": "uniform",
            "反光衣监测": "reflective",
            "反光衣识别": "reflective",
        }

        if inferred_type in fuzzy_mapping:
            matched_key = fuzzy_mapping[inferred_type]
            print(f"  模糊匹配: {inferred_type} -> {matched_key}")
            return matched_key

        # 4. 如果是英文键名，直接检查是否存在
        if inferred_type in MODEL_NAMES:
            print(f"  直接匹配英文键: {inferred_type}")
            return inferred_type

        # 5. 默认降级到通用检测
        print(f"  未找到匹配，降级到通用检测: {inferred_type}")
        return "general"

    def get_current_surveillance_config(self):
        """动态获取当前的监控配置，返回英文键名用于文件命名"""
        surveillance_items = []
        alert_names = "general"

        try:
            # 方法1: 从video_server获取当前算法设置（优先使用完整算法信息）
            from src.video import video_server
            if hasattr(video_server, 'current_algorithm_info') and video_server.current_algorithm_info:
                # 使用完整的算法信息
                surveillance_items = [info['chinese_name'] for info in video_server.current_algorithm_info]
                # 使用第一个算法的英文键名作为alert_names，或者组合多个
                if len(video_server.current_algorithm_info) == 1:
                    alert_names = video_server.current_algorithm_info[0]['english_key']
                else:
                    # 多模型情况下，使用第一个模型的英文键名
                    alert_names = video_server.current_algorithm_info[0]['english_key']
                print(f"从video_server获取完整算法信息: {surveillance_items} -> {alert_names}")
                return surveillance_items, alert_names
            elif hasattr(video_server, 'current_algorithms') and video_server.current_algorithms:
                # 降级到原有逻辑
                surveillance_items = video_server.current_algorithms
                # 转换alert_names为英文键名
                alert_names = self._convert_to_english_keys(video_server.current_algorithms)
                print(f"从video_server获取配置: {surveillance_items} -> {alert_names}")
                return surveillance_items, alert_names
        except (ImportError, AttributeError) as e:
            print(f"从video_server获取算法信息失败: {e}")
            pass

        try:
            # 方法2: 从YAML配置获取当前模型
            from src.core.config_loader import config_loader
            current_model = config_loader.get_current_model()
            model_names = config_loader.get_model_names()

            model_name = current_model['name']
            if model_name in model_names:
                surveillance_items = [model_names[model_name]]
                alert_names = model_name
                print(f"从YAML配置获取: {surveillance_items} -> {alert_names}")
                return surveillance_items, alert_names
        except Exception as e:
            print(f"YAML配置获取失败: {e}")
            pass

        try:
            # 方法3: 重新导入config获取最新配置
            import importlib
            from src.core import config_adapter as config
            importlib.reload(config)

            if isinstance(config.ANOMALY_SURVEILLANCE, list):
                surveillance_items = config.ANOMALY_SURVEILLANCE
            else:
                surveillance_items = [config.ANOMALY_SURVEILLANCE]

            # 转换为英文键名
            alert_names = self._convert_to_english_keys(surveillance_items)
            print(f"从config获取配置: {surveillance_items} -> {alert_names}")
            return surveillance_items, alert_names
        except Exception:
            pass

        # 方法4: 默认配置
        print(f"使用默认配置: ['通用异常检测'] -> general")
        return ["通用异常检测"], "general"

    def _convert_to_english_keys(self, surveillance_items):
        """
        将中文监控项目转换为英文键名
        参数：surveillance_items - 中文监控项目列表
        返回：英文键名（单个模型）或第一个模型的英文键名（多模型）
        """
        if not surveillance_items:
            return "general"

        # 获取最新的MODEL_NAMES（包括自定义模型）
        try:
            from src.core.config_loader import config_loader
            current_model_names = config_loader.get_model_names()
        except:
            current_model_names = MODEL_NAMES

        # 创建中文到英文的映射
        chinese_to_english = {v: k for k, v in current_model_names.items()}

        english_keys = []
        for item in surveillance_items:
            if item in chinese_to_english:
                english_keys.append(chinese_to_english[item])
            else:
                # 如果找不到映射，尝试在MODEL_NAMES的键中查找
                if item in current_model_names:
                    english_keys.append(item)
                else:
                    print(f"⚠️ 未找到 '{item}' 的英文映射，当前可用模型: {list(current_model_names.keys())}")

        if english_keys:
            # 如果是多模型，使用第一个；如果是单模型，直接返回
            result = english_keys[0] if len(english_keys) == 1 else english_keys[0]
            print(f"🔄 转换结果: {surveillance_items} -> {result}")
            return result
        else:
            print(f"⚠️ 无法转换任何项目，使用默认: general")
            return "general"

    def _get_display_alert_type(self, surveillance_items):
        """
        获取用于前端显示的警告类型名称
        参数：surveillance_items - 监控项目列表
        返回：用于显示的警告类型名称
        """
        if not surveillance_items:
            return "通用异常检测"

        if isinstance(surveillance_items, list):
            if len(surveillance_items) == 1:
                # 单模型：直接返回模型名称
                display_name = surveillance_items[0]
                print(f"📱 前端显示类型: {display_name} (单模型)")
                return display_name
            else:
                # 多模型：显示第一个模型名称，或者显示"多模型检测"
                display_name = surveillance_items[0]  # 使用第一个模型
                print(f"📱 前端显示类型: {display_name} (多模型中的第一个)")
                return display_name
        else:
            # 字符串类型
            display_name = str(surveillance_items)
            print(f"📱 前端显示类型: {display_name} (字符串)")
            return display_name
    def trans_date(self, date_str):
        """
        将时间戳转换为中文格式的日期字符串
        参数：date_str - 格式为"年-月-日-时-分-秒"的字符串
        返回：格式化后的中文日期字符串
        """
        # 分解时间成分
        year, month, day, hour, minute, second = date_str.split('-')
        
        # 判断上午/下午
        am_pm = "上午" if int(hour) < 12 else "下午"
        
        # 转换为12小时制
        hour_12 = hour if hour == '12' else str(int(hour) % 12)
        
        # 组合成中文格式
        return f"{year}年{int(month)}月{int(day)}日{am_pm}{hour_12}点（{hour}时）{int(minute)}分{int(second)}秒"
    
    async def analyze(self, frames, fps=20, timestamps=None):
        """
        核心分析方法（异步）
        参数：
            frames - 视频帧列表
            fps - 帧率（默认20）
            timestamps - 时间戳列表
        返回：分析结果字典
        """
        start_time = time.time()  # 记录开始时间
        
        # 初始化历史记录
        histroy = "录像视频刚刚开始。"
        Recursive_summary = ""
        
        # 构建历史上下文
        for i in self.message_queue:
            histroy = "历史视频内容总结:"+Recursive_summary+"\n\n当前时间段：" + i['start_time']+"  - " + i['end_time'] + "\n该时间段视频描述如下：" +i['description'] + "\n\n该时间段异常提醒:"+i['is_alert']
        
        # 并发执行两个分析任务
        time_temp = time.time()
        tasks = [
            chat_request(prompt_summary.format(histroy=histroy)),  # 文本摘要任务
            video_chat_async_limit_frame(prompt_vieo, frames, timestamps, fps=fps)  # 视频分析任务
        ]
        results = await asyncio.gather(*tasks)  # 并行执行
        
        # 解析结果
        Recursive_summary = results[0]  # 历史摘要
        description = results[1]        # 当前视频描述
        description_time = time.time()-time_temp  # 记录耗时

        if timestamps is None:
            return description
        
        # 格式化时间戳
        date_flag = self.trans_date(timestamps[0])+"："
        insert_timestamps=timestamps[0][:10].replace('-', '/')
        # # 保存分析结果
        # if RAGConfig.ENABLE_RAG:
        #     # 保存到数据库
        #     insert_txt([date_flag+description],insert_timestamps, ANOMALY_SURVEILLANCE, 'table_video_table')
        # else:
        #     # 本地保存模式
        #     print("RAG未开启,准备保存到本地")
        #     with open(RAGConfig.HISTORY_FILE, 'a', encoding='utf-8') as file:
        #         print("开始保存历史消息")
        #         file.write(date_flag+description + '\n')

        # 动态获取当前监控配置
        surveillance_items, current_alert_names = self.get_current_surveillance_config()

        text = prompt_detect.format(
            Recursive_summary=Recursive_summary,
            current_time=timestamps[0]+"  - " + timestamps[-1],
            latest_description=description,
            anomaly_surveillance="\n".join([f" - {item}" for item in surveillance_items])
        )
        # 去除模型思考过程

        def think_out(answer: str) -> dict:
            cleaned_text = re.sub(r'<think[^>]*>.*?</think>', '', answer, flags=re.DOTALL)
            # 移除清理后可能在开头的多余换行符
            # final_text = re.sub(r'^\n+', '', cleaned_text)
            return cleaned_text

        # 检测异常
        time_temp = time.time()
        # alert = await chat_request(text)  # 调用异常检测
        alert_think = await chat_request(text)  # 调用异常检测
        # print(alert_think)
        alert = think_out(alert_think)
        # print(alert)
        alert_time = time.time() - time_temp  # 记录检测耗时

        print("警告类型：", surveillance_items)
        print("警告内容：", alert)
        print("\n视频描述原文：\n", description)
        print(f"总耗时: {time.time() - start_time:.2f}秒")
        print(f"描述耗时: {description_time:.2f}秒, 检测耗时: {alert_time:.2f}秒")

        # 保存分析结果
        if RAGConfig.ENABLE_RAG:
            try:
                alert_value=0
                # 直接使用当前选择的模型名称，不再推断
                specific_alert_type = current_alert_names
                if "无异常" not in alert:
                    alert_value=1
                    # 直接使用当前选择的模型名称作为存储类型
                    if isinstance(current_alert_names, list):
                        # 如果是多模型，使用第一个
                        specific_alert_type = current_alert_names[0] if current_alert_names else "general"
                    else:
                        specific_alert_type = current_alert_names

                    # 确保存储类型有效
                    if specific_alert_type not in MODEL_NAMES:
                        print(f"⚠️ 模型名称 '{specific_alert_type}' 不在MODEL_NAMES中，使用general")
                        specific_alert_type = "general"

                    print(f"💾 存储类型: {specific_alert_type} (直接使用当前模型，无需推断)")

                # 保存到数据库
                surveillance_text = "、".join(surveillance_items) if isinstance(surveillance_items, list) else str(surveillance_items)
                result = insert_txt([date_flag+surveillance_text+alert+description],insert_timestamps, specific_alert_type, alert_value,'table_video_table')
                if result["status"] == "fallback_success":
                    print("RAG系统连接失败，已降级到本地文件存储")
                elif result["status"] == "error":
                    print(f"数据存储失败: {result.get('message', '未知错误')}")
            except Exception as e:
                print(f"RAG系统异常: {str(e)}")
                # 直接降级到本地文件存储
                try:
                    with open(RAGConfig.HISTORY_FILE, 'a', encoding='utf-8') as file:
                        file.write(date_flag+description + '\n')
                    print("已降级到本地文件存储")
                except Exception as file_error:
                    print(f"本地文件存储也失败: {str(file_error)}")
        else:
            # 本地保存模式
            print("RAG未开启,准备保存到本地")
            with open(RAGConfig.HISTORY_FILE, 'a', encoding='utf-8') as file:
                print("开始保存历史消息")
                file.write(date_flag+description + '\n')
        # 处理异常情况
        if "无异常" not in alert:
            current_time = timestamps[0]
            # 直接使用当前选择的模型名称生成英文文件名
            file_str = self.generate_english_filename(alert, current_time, current_alert_names)
            new_file_name = f"data/video_warning/{file_str}.mp4"

            # 保存异常视频片段
            os.rename("./data/video_warning/output.mp4", new_file_name)

            # 保存首帧作为封面图
            frame = frames[0]
            if frame.dtype != np.uint8:
                frame = frame.astype(np.uint8)
            if len(frame.shape) == 2:
                frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)  # 灰度转彩色
            # 使用英文文件名保存图片
            cv2.imwrite(f"data/video_warning/{file_str}.jpg", frame)

            # 直接使用当前监控项目名称，不再推断
            display_alert_type = self._get_display_alert_type(surveillance_items)

            return {
                "alert": alert.strip(),  # 移除HTML标签，保持纯文本
                "alert_type": display_alert_type,  # 使用实际的监控项目名称
                "alert_level": "danger",  # 添加警报级别
                "description": f'当前10秒监控消息描述：\n{description}\n\n历史监控内容:\n{Recursive_summary}',
                "video_file_name": f"{file_str}.mp4",
                "picture_file_name": f"{file_str}.jpg",
                "location": "监控区域",  # 添加位置信息
                "details": description  # 添加详细描述
            }
        
        # 记录正常情况
        if timestamps:
            self.message_queue.append({ 
                'start_time': timestamps[0],
                'end_time': timestamps[1],
                'description': description, 
                'is_alert': alert
            })
            # 保持队列长度（最近15条记录）
            self.message_queue = self.message_queue[-15:]
            
        return {"alert": "无异常"}
