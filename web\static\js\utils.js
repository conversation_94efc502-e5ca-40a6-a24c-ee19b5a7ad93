// 通用工具函数

function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg shadow-lg text-white transform translate-x-full transition-transform duration-300`;

    switch (type) {
        case 'success':
            notification.classList.add('bg-green-500');
            break;
        case 'error':
            notification.classList.add('bg-red-500');
            break;
        default:
            notification.classList.add('bg-blue-500');
    }

    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // 显示通知
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// 初始化连接
window.onload = () => {
    connectVideoStream();
    connectAlertService();

    // 初始化视频源配置
    initVideoSourceConfig();

    // 初始化语音助手
    refreshVoiceStatus();
    loadConversationHistory();

    // 定期更新视频状态
    setInterval(updateVideoStatus, 2000);

    // 定期刷新语音状态和对话历史
    setInterval(refreshVoiceStatus, 5000);
    setInterval(loadConversationHistory, 2000); // 更频繁地更新对话记录
};

// 窗口关闭时断开连接
window.onbeforeunload = () => {
    videoSocket?.close();
    alertSocket?.close();
};

// 语音播报开关控制
document.addEventListener('DOMContentLoaded', function() {
    const voiceToggle = document.getElementById('voiceControlToggle');
    if (voiceToggle) {
        voiceToggle.addEventListener('change', (e) => {
            voiceQueue.voiceEnabled = e.target.checked;
            if (!voiceQueue.voiceEnabled) {
                window.speechSynthesis.cancel();
                voiceQueue.queue = [];
                voiceQueue.isSpeaking = false;
            }
        });
    }
});
