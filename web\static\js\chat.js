// 聊天功能
document.addEventListener('DOMContentLoaded', () => {
    const chatContainer = document.getElementById('chatContainer');
    const questionInput = document.getElementById('questionInput');
    const sendButton = document.getElementById('sendQuestion');

    if (!chatContainer || !questionInput || !sendButton) {
        console.log('聊天组件未找到，跳过初始化');
        return;
    }

    async function addMessage(text, isUser = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message rounded-lg p-3 max-w-xs ${isUser ? 'ml-auto bg-purple-600' : 'bg-gray-700'}`;
        messageDiv.innerHTML = `
            <p class="text-sm">${text}</p>
            <p class="text-xs text-gray-400 mt-1 text-right">${isUser ? '用户' : '系统消息'}</p>
        `;
        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }

    async function handleQuestion() {
        const question = questionInput.value.trim();
        if (!question) return;

        addMessage(question, true);
        questionInput.value = '';

        try {
            const response = await fetch('/api/ask', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ question })
            });

            const data = await response.json();
            addMessage(data.answer);
        } catch (error) {
            addMessage('回答获取失败，请稍后再试');
            console.error('Error:', error);
        }
    }

    sendButton.addEventListener('click', handleQuestion);
    questionInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') handleQuestion();
    });
});
