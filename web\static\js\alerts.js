/**
 * 警报处理模块
 * 监听新警报事件并显示在界面上
 */

// 警报计数器
let alertCount = 0;

/**
 * 初始化警报处理
 */
function initAlertHandler() {
    console.log('初始化警报处理模块...');

    // 监听新警报事件
    document.addEventListener('newAlert', handleNewAlert);

    console.log('警报处理模块初始化完成');
}

/**
 * 处理新警报
 */
function handleNewAlert(event) {
    try {
        const alertData = event.detail;
        console.log('处理新警报:', alertData);

        // 显示警报
        displayAlert(alertData);

        // 语音播报
        if (typeof voiceQueue !== 'undefined' && voiceQueue.addToQueue) {
            const speechText = alertData.alert.replace(/<[^>]*>/g, '');
            voiceQueue.addToQueue(speechText);
        }

        // 更新计数器
        updateAlertCount();

    } catch (error) {
        console.error('处理警报数据错误:', error);
    }
}

/**
 * 显示警报
 */
function displayAlert(alertData) {
    const alertList = document.getElementById('alerts-list');
    if (!alertList) {
        console.error('找不到警报列表元素');
        return;
    }

    // 清除默认的"暂无警报信息"提示
    if (alertList.children.length === 1 && alertList.children[0].textContent.includes('暂无警报信息')) {
        alertList.innerHTML = '';
    }

    // 确保位置信息存在
    const location = alertData.location || '监控区域';

    // 创建警报元素
    const alertItem = document.createElement('div');
    alertItem.className = 'alert-item bg-gray-800 p-4 rounded-lg mb-3';

    // 根据警报级别设置样式
    const alertLevel = alertData.alert_level || 'warning';
    const borderColor = alertLevel === 'danger' ? 'border-red-500' : 'border-yellow-500';
    const bgColor = alertLevel === 'danger' ? 'bg-red-900' : 'bg-yellow-900';

    alertItem.classList.add('border-l-4', borderColor);

    // 格式化时间
    const timestamp = alertData.timestamp ? new Date(alertData.timestamp).toLocaleString('zh-CN') : new Date().toLocaleString('zh-CN');

    alertItem.innerHTML = `
        <div class="flex justify-between items-start mb-2">
            <div>
                <h4 class="font-medium text-white">${alertData.alert_type || '异常检测'}</h4>
                <p class="text-sm text-gray-400">${timestamp}</p>
            </div>
            <span class="text-xs px-2 py-1 rounded ${alertLevel === 'danger' ? 'bg-red-600' : 'bg-yellow-600'} text-white">
                ${alertLevel === 'danger' ? '危险' : '警告'}
            </span>
        </div>
        <div class="text-sm text-gray-300 mb-2">
            <strong>位置：</strong>${location}
        </div>
        <div class="text-sm text-gray-300 mb-2">
            <strong>详情：</strong>${alertData.alert || alertData.details || '检测到异常行为'}
        </div>
        ${alertData.description ? `
        <div class="text-xs text-gray-400 mt-2 p-2 bg-gray-700 rounded">
            <strong>描述：</strong>${alertData.description.substring(0, 100)}${alertData.description.length > 100 ? '...' : ''}
        </div>
        ` : ''}
        ${alertData.picture_file_name ? `
        <div class="text-xs text-gray-400 mt-1">
            <i class="fas fa-image mr-1"></i>图片: ${alertData.picture_file_name}
        </div>
        ` : ''}
        ${alertData.video_file_name ? `
        <div class="text-xs text-gray-400 mt-1">
            <i class="fas fa-video mr-1"></i>视频: ${alertData.video_file_name}
        </div>
        ` : ''}
    `;

    // 添加到列表顶部
    alertList.prepend(alertItem);

    // 限制显示的警报数量
    const maxAlerts = 10;
    while (alertList.children.length > maxAlerts) {
        alertList.removeChild(alertList.lastChild);
    }

    // 添加动画效果
    alertItem.style.opacity = '0';
    alertItem.style.transform = 'translateY(-20px)';

    setTimeout(() => {
        alertItem.style.transition = 'all 0.3s ease';
        alertItem.style.opacity = '1';
        alertItem.style.transform = 'translateY(0)';
    }, 10);
}

/**
 * 更新警报计数器
 */
function updateAlertCount() {
    alertCount++;

    const alertCountElement = document.getElementById('alertCount');
    if (alertCountElement) {
        alertCountElement.textContent = alertCount;
        alertCountElement.classList.add('animate-pulse');

        setTimeout(() => {
            alertCountElement.classList.remove('animate-pulse');
        }, 1000);
    }
}

/**
 * 清空警报列表
 */
function clearAlerts() {
    const alertList = document.getElementById('alerts-list');
    if (alertList) {
        alertList.innerHTML = `
            <div class="text-center text-gray-400 text-sm py-8">
                <i class="fas fa-shield-alt text-2xl mb-2"></i>
                <p>暂无警报信息</p>
            </div>
        `;
        alertCount = 0;
        updateAlertCount();

        // 停止演示模式的警报生成
        if (window.appState && window.appState.demoMode) {
            window.appState.demoMode = false;
            console.log('已停止演示模式的警报生成');
        }
    }
}

/**
 * 生成测试警报
 */
function generateTestAlert() {
    const testAlerts = [
        {
            alert: "检测到未佩戴安全帽的情况",
            alert_type: "安全帽检测",
            alert_level: "danger",
            location: "生产车间A区",
            timestamp: new Date().toISOString(),
            description: "工作人员在进入危险区域时未佩戴安全帽，存在安全隐患。",
            picture_file_name: "helmet_test.jpg"
        },
        {
            alert: "检测到吸烟行为",
            alert_type: "吸烟检测",
            alert_level: "warning",
            location: "办公区域",
            timestamp: new Date().toISOString(),
            description: "在禁烟区域检测到吸烟行为，请立即停止。",
            video_file_name: "smoking_test.mp4"
        },
        {
            alert: "检测到人员跌倒",
            alert_type: "跌倒检测",
            alert_level: "danger",
            location: "走廊区域",
            timestamp: new Date().toISOString(),
            description: "检测到人员可能发生跌倒，请立即查看现场情况。"
        }
    ];

    const randomAlert = testAlerts[Math.floor(Math.random() * testAlerts.length)];

    // 触发警报事件
    const alertEvent = new CustomEvent('newAlert', {
        detail: randomAlert
    });
    document.dispatchEvent(alertEvent);

    console.log('生成测试警报:', randomAlert);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initAlertHandler();

    // 绑定测试警报按钮
    const testAlertBtn = document.getElementById('testAlertBtn');
    if (testAlertBtn) {
        testAlertBtn.addEventListener('click', generateTestAlert);
    }

    // 绑定清除警报按钮
    const clearAlertsBtn = document.getElementById('clearAlertsBtn');
    if (clearAlertsBtn) {
        clearAlertsBtn.addEventListener('click', function() {
            clearAlerts();
            showNotification('警报已清除', 'info');
        });
    }
});
