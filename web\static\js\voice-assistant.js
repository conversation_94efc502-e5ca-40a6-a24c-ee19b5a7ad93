// 语音助手相关功能
let voiceStatus = {
    active: false,
    listening: false,
    conversation_active: false
};

// 启动语音助手
async function startVoiceAssistant() {
    try {
        showNotification('正在启动语音助手...', 'info');

        // 使用正确的后端服务地址和端口
        const host = window.location.hostname;
        const port = '16532'; // 后端服务端口
        const apiUrl = `http://${host}:${port}/api/voice/control`;

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ action: 'start' })
        });

        const result = await response.json();

        if (result.status === 'success') {
            showNotification('语音助手启动成功！', 'success');
            updateVoiceUI(result.data);
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('启动失败:', error);
        showNotification('启动失败: ' + error.message, 'error');
    }
}

// 停止语音助手
async function stopVoiceAssistant() {
    try {
        showNotification('正在停止语音助手...', 'info');

        // 使用正确的后端服务地址和端口
        const host = window.location.hostname;
        const port = '16532'; // 后端服务端口
        const apiUrl = `http://${host}:${port}/api/voice/control`;

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ action: 'stop' })
        });

        const result = await response.json();

        if (result.status === 'success') {
            showNotification('语音助手已停止', 'success');
            updateVoiceUI(result.data);
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('停止失败:', error);
        showNotification('停止失败: ' + error.message, 'error');
    }
}

// 更新语音UI状态
function updateVoiceUI(status) {
    console.log('updateVoiceUI 被调用，状态:', status); // 添加调试日志
    voiceStatus = status;

    // 更新助手状态
    const assistantStatusEl = document.getElementById('assistantStatus');
    if (assistantStatusEl) {
        const newText = status.active ? '运行中' : '未启动';
        const newClass = `px-2 py-1 rounded text-xs ${status.active ? 'bg-green-600' : 'bg-red-600'}`;
        console.log('更新助手状态:', newText, newClass); // 添加调试日志
        assistantStatusEl.textContent = newText;
        assistantStatusEl.className = newClass;
    } else {
        console.warn('找不到 assistantStatus 元素');
    }

    // 更新监听状态
    const listeningStatusEl = document.getElementById('listeningStatus');
    if (listeningStatusEl) {
        listeningStatusEl.textContent = status.listening ? '监听中' : '未监听';
        listeningStatusEl.className = `px-2 py-1 rounded text-xs ${status.listening ? 'bg-blue-600' : 'bg-gray-600'}`;
    }

    // 更新对话状态
    const conversationStatusEl = document.getElementById('conversationStatus');
    if (conversationStatusEl) {
        conversationStatusEl.textContent = status.conversation_active ? '对话中' : '未对话';
        conversationStatusEl.className = `px-2 py-1 rounded text-xs ${status.conversation_active ? 'bg-purple-600' : 'bg-gray-600'}`;
    }

    // 更新休眠状态
    const sleepStatusEl = document.getElementById('sleepStatus');
    if (sleepStatusEl) {
        const isSleeping = status.sleeping || false;
        sleepStatusEl.textContent = isSleeping ? '休眠中' : '正常';
        sleepStatusEl.className = `px-2 py-1 rounded text-xs ${isSleeping ? 'bg-yellow-600' : 'bg-green-600'}`;
    }

    // 更新按钮状态
    const startBtn = document.getElementById('startVoiceBtn');
    const stopBtn = document.getElementById('stopVoiceBtn');
    if (startBtn) startBtn.disabled = status.active;
    if (stopBtn) stopBtn.disabled = !status.active;
}

// 快捷播放
async function quickSpeak(text) {
    try {
        // 使用正确的后端服务地址和端口
        const host = window.location.hostname;
        const port = '16532'; // 后端服务端口
        const apiUrl = `http://${host}:${port}/api/voice/speak`;

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ text: text })
        });

        const result = await response.json();

        if (result.status === 'success') {
            showNotification('语音播放成功', 'success');
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('播放失败:', error);
        showNotification('播放失败: ' + error.message, 'error');
    }
}

// 快捷查询
async function quickQuery(text) {
    try {
        // 使用正确的后端服务地址和端口
        const host = window.location.hostname;
        const port = '16532'; // 后端服务端口
        const apiUrl = `http://${host}:${port}/api/voice/query`;

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ text: text, use_context: true })
        });

        const result = await response.json();

        if (result.status === 'success') {
            addConversationBubble('user', text);
            addConversationBubble('assistant', result.data.response);
            showNotification('查询成功', 'success');
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('查询失败:', error);
        showNotification('查询失败: ' + error.message, 'error');
    }
}

// 清空对话历史
async function clearConversation() {
    try {
        // 使用正确的后端服务地址和端口
        const host = window.location.hostname;
        const port = '16532'; // 后端服务端口
        const apiUrl = `http://${host}:${port}/api/voice/conversation-history`;

        const response = await fetch(apiUrl, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.status === 'success') {
            const conversationHistory = document.getElementById('conversationHistory');
            if (conversationHistory) {
                conversationHistory.innerHTML = `
                    <div class="text-center text-gray-400">
                        <i class="fas fa-comments text-2xl mb-2"></i>
                        <p>对话历史已清空</p>
                    </div>
                `;

                // 更新统计
                const countElement = document.getElementById('conversationCount');
                if (countElement) {
                    countElement.textContent = '0';
                }
            }
            showNotification('对话历史已清空', 'success');
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('清空失败:', error);
        showNotification('清空失败: ' + error.message, 'error');
    }
}

// 刷新语音状态
async function refreshVoiceStatus() {
    try {
        // 使用正确的后端服务地址和端口
        const host = window.location.hostname;
        const port = '16532'; // 后端服务端口
        const apiUrl = `http://${host}:${port}/api/voice/status`;

        const response = await fetch(apiUrl);
        if (response.ok) {
            const result = await response.json();
            console.log('语音状态API响应:', result); // 添加调试日志
            if (result.status === 'success') {
                console.log('更新语音UI状态:', result.data); // 添加调试日志
                updateVoiceUI(result.data);
            }
        } else {
            console.warn('语音状态API响应失败:', response.status, response.statusText);
        }
    } catch (error) {
        console.error('刷新语音状态失败:', error);
    }
}

// 添加对话气泡
function addConversationBubble(role, content) {
    const conversationHistory = document.getElementById('conversationHistory');
    if (!conversationHistory) {
        console.error('找不到对话历史容器元素');
        return;
    }

    const isUser = role === 'user';

    const bubble = document.createElement('div');
    bubble.className = `mb-3 p-2 rounded-lg border-l-4 ${
        isUser ? 'bg-blue-900 border-blue-400 text-blue-100' : 'bg-green-900 border-green-400 text-green-100'
    }`;

    const time = new Date().toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    bubble.innerHTML = `
        <div class="flex items-center mb-1">
            <i class="fas fa-${isUser ? 'user' : 'robot'} mr-2"></i>
            <span class="font-medium">${isUser ? '用户' : '助手'}</span>
            <span class="text-xs ml-auto opacity-70">${time}</span>
        </div>
        <div class="text-sm">${content}</div>
    `;

    // 如果是空状态，先清空
    if (conversationHistory.innerHTML.includes('暂无对话记录') || conversationHistory.innerHTML.includes('对话历史已清空')) {
        conversationHistory.innerHTML = '';
    }

    conversationHistory.appendChild(bubble);
    conversationHistory.scrollTop = conversationHistory.scrollHeight;

    // 更新统计
    const countElement = document.getElementById('conversationCount');
    if (countElement) {
        const messageCount = conversationHistory.children.length;
        countElement.textContent = messageCount;
    }
}

// 加载对话历史
async function loadConversationHistory() {
    try {
        console.log('正在加载对话历史...');

        // 尝试从API获取数据
        let result = null;
        try {
            // 使用正确的后端服务地址和端口
        const host = window.location.hostname;
        const port = '16532'; // 后端服务端口
        const apiUrl = `http://${host}:${port}/api/voice/conversation-history`;

        const response = await fetch(apiUrl);
            console.log('API响应状态:', response.status);

            if (response.ok) {
                result = await response.json();
                console.log('对话历史数据:', result);
            }
        } catch (apiError) {
            console.warn('API调用失败，尝试从文件读取:', apiError);

            // 尝试从静态文件读取对话历史
            try {
                const fileResponse = await fetch('/data/voice/conversation_history.json');
                if (fileResponse.ok) {
                    const fileData = await fileResponse.json();
                    result = {
                        status: 'success',
                        message: '从文件获取对话历史',
                        data: {
                            history: fileData,
                            count: fileData.length
                        }
                    };
                    console.log('从文件获取到对话历史:', fileData.length, '条');
                } else {
                    throw new Error('文件读取失败');
                }
            } catch (fileError) {
                console.warn('文件读取也失败，显示空状态:', fileError);
                result = {
                    status: 'success',
                    message: '无对话历史',
                    data: {
                        history: [],
                        count: 0
                    }
                };
            }
        }

        // 更新语音控制模块中的对话记录区域
        const historyContainer = document.getElementById('conversationHistory');

        if (result.status === 'success' && result.data.history.length > 0) {
            historyContainer.innerHTML = '';

            result.data.history.forEach(message => {
                const messageDiv = document.createElement('div');
                const isUser = message.role === 'user';

                messageDiv.className = `mb-3 p-2 rounded-lg border-l-4 ${
                    isUser ? 'bg-blue-900 border-blue-400 text-blue-100' : 'bg-green-900 border-green-400 text-green-100'
                }`;

                const time = new Date(message.timestamp).toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });

                messageDiv.innerHTML = `
                    <div class="flex items-center mb-1">
                        <i class="fas fa-${isUser ? 'user' : 'robot'} mr-2"></i>
                        <span class="font-medium">${isUser ? '用户' : '助手'}</span>
                        <span class="text-xs ml-auto opacity-70">${time}</span>
                    </div>
                    <div class="text-sm">${message.content}</div>
                `;

                historyContainer.appendChild(messageDiv);
            });

            // 滚动到底部
            historyContainer.scrollTop = historyContainer.scrollHeight;
        } else {
            historyContainer.innerHTML = `
                <div class="text-center text-gray-500">
                    <i class="fas fa-comments mb-1"></i>
                    <p>暂无对话记录</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('加载对话历史失败:', error);
        const historyContainer = document.getElementById('conversationHistory');
        if (historyContainer) {
            historyContainer.innerHTML = `
                <div class="text-center text-red-500">
                    <i class="fas fa-exclamation-triangle mb-1"></i>
                    <p>加载失败</p>
                </div>
            `;
        }
    }
}
