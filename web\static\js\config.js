/**
 * 系统配置页面JavaScript
 * 处理配置的加载、保存和重置功能
 */

// 配置API基础URL
const CONFIG_API_BASE = (() => {
    const host = window.location.hostname;
    const port = '16532'; // 后端服务端口
    return `http://${host}:${port}`;
})();

/**
 * 显示通知消息
 */
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-600' : 
        type === 'error' ? 'bg-red-600' : 
        type === 'warning' ? 'bg-yellow-600' : 'bg-blue-600'
    } text-white`;
    
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${
                type === 'success' ? 'check-circle' : 
                type === 'error' ? 'exclamation-circle' : 
                type === 'warning' ? 'exclamation-triangle' : 'info-circle'
            } mr-2"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

/**
 * 加载系统配置
 */
async function loadConfig() {
    try {
        showNotification('正在加载配置...', 'info');
        
        const response = await fetch(`${CONFIG_API_BASE}/api/config`);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (result.status === 'success') {
            // 更新界面配置值
            updateConfigUI(result.config);
            showNotification('配置加载成功', 'success');
        } else {
            throw new Error(result.message || '加载配置失败');
        }
        
    } catch (error) {
        console.error('加载配置失败:', error);
        showNotification(`加载配置失败: ${error.message}`, 'error');
    }
}

/**
 * 更新配置界面
 */
function updateConfigUI(config) {
    // 视频配置
    if (config.video_source) {
        const videoSourceElement = document.getElementById('currentVideoSource');
        if (videoSourceElement) {
            videoSourceElement.textContent = config.video_source;
        }
    }
    
    // 模型配置
    if (config.current_model) {
        const modelElement = document.getElementById('currentModel');
        if (modelElement) {
            modelElement.textContent = config.current_model.name || '未设置';
        }
    }
    
    // 其他配置项可以根据需要添加
    console.log('配置已更新:', config);
}

/**
 * 保存配置
 */
async function saveConfig(configData) {
    try {
        showNotification('正在保存配置...', 'info');
        
        const response = await fetch(`${CONFIG_API_BASE}/api/config`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(configData)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (result.status === 'success') {
            showNotification('配置保存成功', 'success');
        } else {
            throw new Error(result.message || '保存配置失败');
        }
        
    } catch (error) {
        console.error('保存配置失败:', error);
        showNotification(`保存配置失败: ${error.message}`, 'error');
    }
}

/**
 * 重置配置为默认值
 */
function resetConfig() {
    if (confirm('确定要重置所有配置为默认值吗？此操作不可撤销。')) {
        showNotification('重置功能暂未实现', 'warning');
        // TODO: 实现重置逻辑
    }
}

/**
 * 应用视频配置
 */
async function applyVideoConfig() {
    const videoConfig = {
        analysis_interval: document.getElementById('analysisInterval')?.value || 10,
        buffer_duration: document.getElementById('bufferDuration')?.value || 11,
        jpeg_quality: document.getElementById('jpegQuality')?.value || 70
    };
    
    await saveConfig({ video: videoConfig });
}

/**
 * 应用音频配置
 */
async function applyAudioConfig() {
    const audioConfig = {
        enabled: document.getElementById('audioEnabled')?.checked || false,
        sample_rate: document.getElementById('sampleRate')?.value || 16000,
        channels: document.getElementById('channels')?.value || 1
    };
    
    await saveConfig({ audio: audioConfig });
}

/**
 * 应用唤醒词配置
 */
async function applyWakeWordConfig() {
    const wakeWords = document.getElementById('wakeWords')?.value?.split(',').map(w => w.trim()) || [];
    const wakeWordConfig = {
        wake_words: wakeWords,
        sensitivity: document.getElementById('wakeWordSensitivity')?.value || 0.8
    };
    
    await saveConfig({ wake_word: wakeWordConfig });
}

/**
 * 应用存储配置
 */
async function applyStorageConfig() {
    const storageConfig = {
        video_path: document.getElementById('videoPath')?.value || 'data/video_warning',
        audio_path: document.getElementById('audioPath')?.value || 'data/voice',
        max_storage_days: document.getElementById('maxStorageDays')?.value || 30
    };
    
    await saveConfig({ storage: storageConfig });
}

/**
 * 应用API配置
 */
async function applyApiConfig() {
    const apiConfig = {
        qwen: {
            api_url: document.getElementById('qwenApiUrl')?.value || '',
            model: document.getElementById('qwenModel')?.value || ''
        },
        moonshot: {
            api_url: document.getElementById('moonshotApiUrl')?.value || '',
            model: document.getElementById('moonshotModel')?.value || ''
        },
        timeout: parseFloat(document.getElementById('requestTimeout')?.value || 60),
        temperature: parseFloat(document.getElementById('temperature')?.value || 0.5)
    };
    
    await saveConfig({ api: apiConfig });
}

/**
 * 应用RAG配置
 */
async function applyRagConfig() {
    const ragConfig = {
        milvus: {
            host: document.getElementById('milvusHost')?.value || 'localhost',
            port: parseInt(document.getElementById('milvusPort')?.value || 19530),
            embedding_dim: parseInt(document.getElementById('embeddingDim')?.value || 768)
        },
        vector_api_url: document.getElementById('vectorApiUrl')?.value || '',
        history_file: document.getElementById('historyFile')?.value || ''
    };
    
    await saveConfig({ rag: ragConfig });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('配置页面初始化...');
    
    // 绑定按钮事件
    const loadConfigBtn = document.getElementById('loadConfig');
    if (loadConfigBtn) {
        loadConfigBtn.addEventListener('click', loadConfig);
    }
    
    const resetConfigBtn = document.getElementById('resetConfig');
    if (resetConfigBtn) {
        resetConfigBtn.addEventListener('click', resetConfig);
    }
    
    // 绑定各个配置保存按钮
    const configButtons = [
        { id: 'applyVideoConfig', handler: applyVideoConfig },
        { id: 'applyAudioConfig', handler: applyAudioConfig },
        { id: 'applyWakeWordConfig', handler: applyWakeWordConfig },
        { id: 'applyStorageConfig', handler: applyStorageConfig },
        { id: 'applyApiConfig', handler: applyApiConfig },
        { id: 'applyRagConfig', handler: applyRagConfig }
    ];
    
    configButtons.forEach(({ id, handler }) => {
        const button = document.getElementById(id);
        if (button) {
            button.addEventListener('click', handler);
        }
    });
    
    // 自动加载当前配置
    loadConfig();
    
    console.log('配置页面初始化完成');
});
