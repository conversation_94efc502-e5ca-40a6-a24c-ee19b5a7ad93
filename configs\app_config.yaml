api:
  moonshot:
    api_key: ''
    api_url: http://************:11434/v1/chat/completions
    model: qwen3:4b
  qwen:
    api_key: ''
    api_url: http://************:11434/v1/chat/completions
    model: gemma3:4b
#  moonshot:
#    api_key: sk-537a5e755c5844b9ac57a3ca0da8c81a
#    api_url: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions
#    model: qwen-plus-latest
#  qwen:
#    api_key: sk-537a5e755c5844b9ac57a3ca0da8c81a
#    api_url: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions
#    model: qwen2-vl-2b-instruct
  request:
    repetition_penalty: 1.05
    temperature: 0.5
    timeout: 60.0
    top_k: 20
    top_p: 0.01
logging:
  format: '%(asctime)s - %(levelname)s - %(message)s'
  handlers:
  - filename: logs/app.log
    type: file
  - type: stream
  level: INFO
models:
  current_model:
    name: helmet
    timestamp: null
  model_names:
    chef-hat: 厨师帽识别
    counting: 计数检测
    custom: 自定义模型
    detection_mask: 白色口罩检测
    detection_mask_0001: 蓝色口罩检测
    dust: 烟尘检测
    face: 人脸识别
    fall: 跌倒检测
    fence: 电子围栏
    fight: 打架斗殴检测
    fighting: 打架识别
    fire: 火焰烟火检测
    fire-exit: 消防通道阻塞识别
    general: 通用异常检测
    helmet: 安全帽检测
    helmet_0001: 蓝色安全帽检查
    helmet_detection: 红色安全帽检测
    intrusion: 入侵检测
    leave: 离岗检测
    license-plate: 车牌识别
    line-crossing: 越线计数
    loitering: 徘徊检测
    mask: 口罩检测
    open-fire: 明火检测
    overcrowd: 人员超限检测
    perimeter: 周界入侵检测
    phone: 打电话检测
    reflective: 反光衣识别
    sleep: 睡岗检测
    smoking: 吸烟检测
    standard-operation: 规范作业检测
    stranger: 陌生人告警
    uniform: 工作服检测
    weapon: 持械识别
rag:
  enable_rag: true
  history_file: video_histroy_info.txt
  milvus:
    collection_name: table_test_table
    embedding_dim: 768
    host: ************
    password: Milvus
    port: '19530'
    user: root
  vector_api_url: http://************:11434/api/embeddings
server:
  host: 0.0.0.0
  port: 16532
  reload: true
  workers: 1
storage:
  archive_dir: data/archive
  uploads_dir: data/uploads
  video_warning_dir: data/video_warning
  voice_dir: data/voice
video:
  processing:
    analysis_interval: 10
    buffer_duration: 11
    jpeg_quality: 70
    max_ws_queue: 100
    video_interval: 1800
    ws_retry_interval: 3
  source: test/test3.mp4
