# ESP32硬件实时语音问答开发方案

## 🎯 开发总体规划

### 开发阶段划分
```mermaid
gantt
    title ESP32语音问答开发时间线
    dateFormat  YYYY-MM-DD
    section 硬件准备
    硬件采购        :done, hw1, 2024-01-01, 3d
    硬件组装测试    :done, hw2, after hw1, 2d
    
    section ESP32开发
    I2S音频驱动     :active, esp1, 2024-01-06, 5d
    网络通信模块    :esp2, after esp1, 3d
    主控制逻辑      :esp3, after esp2, 4d
    
    section 电脑端开发
    ESP32接口模块   :pc1, 2024-01-10, 4d
    音频处理优化    :pc2, after pc1, 3d
    
    section 集成测试
    单元测试        :test1, after esp3, 2d
    集成测试        :test2, after test1, 3d
    性能优化        :opt1, after test2, 5d
```

## 🔧 ESP32端详细开发方案

### 1. 项目结构设计

```
ESP32_VoiceAssistant/
├── src/
│   ├── main.cpp                 # 主程序入口
│   ├── audio/
│   │   ├── i2s_recorder.cpp     # I2S录音模块
│   │   ├── i2s_player.cpp       # I2S播放模块
│   │   └── audio_buffer.cpp     # 音频缓冲管理
│   ├── network/
│   │   ├── wifi_manager.cpp     # WiFi连接管理
│   │   ├── websocket_client.cpp # WebSocket客户端
│   │   └── audio_protocol.cpp   # 音频传输协议
│   ├── control/
│   │   ├── voice_detector.cpp   # 语音活动检测
│   │   ├── led_controller.cpp   # LED状态指示
│   │   └── button_handler.cpp   # 按键处理
│   └── utils/
│       ├── config.h             # 配置文件
│       └── logger.cpp           # 日志系统
├── lib/                         # 第三方库
├── data/                        # 配置文件
└── platformio.ini               # PlatformIO配置
```

### 2. 核心配置文件

```cpp
#ifndef CONFIG_H
#define CONFIG_H

// === 硬件引脚配置 ===
// I2S麦克风 (INMP441)
#define I2S_MIC_WS_PIN      32
#define I2S_MIC_SCK_PIN     33  
#define I2S_MIC_SD_PIN      34

// I2S功放 (MAX98357A)
#define I2S_SPK_WS_PIN      21
#define I2S_SPK_SCK_PIN     22
#define I2S_SPK_SD_PIN      25

// 状态指示LED
#define LED_PIN             2
#define BUTTON_PIN          0

// === 音频参数配置 ===
#define SAMPLE_RATE         16000
#define BITS_PER_SAMPLE     16
#define CHANNELS            1
#define DMA_BUF_COUNT       8
#define DMA_BUF_LEN         1024

// === 网络配置 ===
#define WIFI_SSID           "Your_WiFi_SSID"
#define WIFI_PASSWORD       "Your_WiFi_Password"
#define SERVER_IP           "*************"  // 电脑IP
#define SERVER_PORT         8765
#define WEBSOCKET_PATH      "/voice"

// === 音频处理配置 ===
#define RECORD_BUFFER_SIZE  (SAMPLE_RATE * 2 * 5)  // 5秒缓冲
#define PLAY_BUFFER_SIZE    (SAMPLE_RATE * 2 * 10) // 10秒缓冲
#define VAD_THRESHOLD       1000    // 语音活动检测阈值
#define SILENCE_TIMEOUT     2000    // 静音超时(ms)

// === 系统配置 ===
#define WATCHDOG_TIMEOUT    30000   // 看门狗超时(ms)
#define RECONNECT_INTERVAL  5000    // 重连间隔(ms)
#define MAX_RECONNECT_TRIES 10      // 最大重连次数

#endif
```

### 3. I2S音频录音模块

```cpp
#include "i2s_recorder.h"
#include "driver/i2s.h"
#include "config.h"

I2SRecorder::I2SRecorder() : is_recording(false), vad_enabled(true) {
    record_buffer = (int16_t*)malloc(RECORD_BUFFER_SIZE);
    if (!record_buffer) {
        Serial.println("❌ 录音缓冲区分配失败");
    }
}

bool I2SRecorder::init() {
    // I2S配置 - 录音
    i2s_config_t i2s_config = {
        .mode = (i2s_mode_t)(I2S_MODE_MASTER | I2S_MODE_RX),
        .sample_rate = SAMPLE_RATE,
        .bits_per_sample = I2S_BITS_PER_SAMPLE_16BIT,
        .channel_format = I2S_CHANNEL_FMT_ONLY_LEFT,
        .communication_format = I2S_COMM_FORMAT_I2S,
        .intr_alloc_flags = ESP_INTR_FLAG_LEVEL1,
        .dma_desc_num = DMA_BUF_COUNT,
        .dma_frame_num = DMA_BUF_LEN,
        .use_apll = false,
        .tx_desc_auto_clear = false
    };

    // I2S引脚配置
    i2s_pin_config_t pin_config = {
        .bck_io_num = I2S_MIC_SCK_PIN,
        .ws_io_num = I2S_MIC_WS_PIN,
        .data_out_num = I2S_PIN_NO_CHANGE,
        .data_in_num = I2S_MIC_SD_PIN
    };

    // 安装I2S驱动
    esp_err_t err = i2s_driver_install(I2S_NUM_0, &i2s_config, 0, NULL);
    if (err != ESP_OK) {
        Serial.printf("❌ I2S录音驱动安装失败: %s\n", esp_err_to_name(err));
        return false;
    }

    // 设置I2S引脚
    err = i2s_set_pin(I2S_NUM_0, &pin_config);
    if (err != ESP_OK) {
        Serial.printf("❌ I2S录音引脚设置失败: %s\n", esp_err_to_name(err));
        return false;
    }

    Serial.println("✅ I2S录音模块初始化成功");
    return true;
}

bool I2SRecorder::startRecording() {
    if (is_recording) {
        return true;
    }

    // 清空缓冲区
    i2s_zero_dma_buffer(I2S_NUM_0);
    
    buffer_pos = 0;
    silence_start = 0;
    is_recording = true;
    
    Serial.println("🎤 开始录音...");
    return true;
}

size_t I2SRecorder::readAudio(int16_t* buffer, size_t max_samples) {
    if (!is_recording) {
        return 0;
    }

    size_t bytes_read = 0;
    esp_err_t err = i2s_read(I2S_NUM_0, buffer, max_samples * sizeof(int16_t), 
                            &bytes_read, portMAX_DELAY);
    
    if (err != ESP_OK) {
        Serial.printf("❌ I2S读取失败: %s\n", esp_err_to_name(err));
        return 0;
    }

    size_t samples_read = bytes_read / sizeof(int16_t);
    
    // 语音活动检测
    if (vad_enabled && samples_read > 0) {
        bool voice_detected = detectVoiceActivity(buffer, samples_read);
        updateSilenceTimer(voice_detected);
    }

    return samples_read;
}

bool I2SRecorder::detectVoiceActivity(int16_t* samples, size_t count) {
    // 计算音频能量
    long energy = 0;
    for (size_t i = 0; i < count; i++) {
        energy += abs(samples[i]);
    }
    energy /= count;

    // 简单的能量阈值检测
    return energy > VAD_THRESHOLD;
}

void I2SRecorder::updateSilenceTimer(bool voice_detected) {
    unsigned long now = millis();
    
    if (voice_detected) {
        silence_start = 0;  // 重置静音计时
    } else {
        if (silence_start == 0) {
            silence_start = now;  // 开始静音计时
        }
    }
}

bool I2SRecorder::isSilenceTimeout() {
    if (silence_start == 0) {
        return false;
    }
    return (millis() - silence_start) > SILENCE_TIMEOUT;
}

void I2SRecorder::stopRecording() {
    if (!is_recording) {
        return;
    }
    
    is_recording = false;
    Serial.println("⏹️ 停止录音");
}

AudioData I2SRecorder::getRecordedAudio() {
    AudioData data;
    data.samples = record_buffer;
    data.sample_count = buffer_pos;
    data.sample_rate = SAMPLE_RATE;
    data.channels = CHANNELS;
    data.bits_per_sample = BITS_PER_SAMPLE;
    
    return data;
}
```

### 4. I2S音频播放模块

```cpp
#include "i2s_player.h"
#include "driver/i2s.h"
#include "config.h"

I2SPlayer::I2SPlayer() : is_playing(false) {
    play_buffer = (int16_t*)malloc(PLAY_BUFFER_SIZE);
    if (!play_buffer) {
        Serial.println("❌ 播放缓冲区分配失败");
    }
}

bool I2SPlayer::init() {
    // I2S配置 - 播放
    i2s_config_t i2s_config = {
        .mode = (i2s_mode_t)(I2S_MODE_MASTER | I2S_MODE_TX),
        .sample_rate = SAMPLE_RATE,
        .bits_per_sample = I2S_BITS_PER_SAMPLE_16BIT,
        .channel_format = I2S_CHANNEL_FMT_ONLY_LEFT,
        .communication_format = I2S_COMM_FORMAT_I2S,
        .intr_alloc_flags = ESP_INTR_FLAG_LEVEL1,
        .dma_desc_num = DMA_BUF_COUNT,
        .dma_frame_num = DMA_BUF_LEN,
        .use_apll = false,
        .tx_desc_auto_clear = true
    };

    // I2S引脚配置
    i2s_pin_config_t pin_config = {
        .bck_io_num = I2S_SPK_SCK_PIN,
        .ws_io_num = I2S_SPK_WS_PIN,
        .data_out_num = I2S_SPK_SD_PIN,
        .data_in_num = I2S_PIN_NO_CHANGE
    };

    // 安装I2S驱动
    esp_err_t err = i2s_driver_install(I2S_NUM_1, &i2s_config, 0, NULL);
    if (err != ESP_OK) {
        Serial.printf("❌ I2S播放驱动安装失败: %s\n", esp_err_to_name(err));
        return false;
    }

    // 设置I2S引脚
    err = i2s_set_pin(I2S_NUM_1, &pin_config);
    if (err != ESP_OK) {
        Serial.printf("❌ I2S播放引脚设置失败: %s\n", esp_err_to_name(err));
        return false;
    }

    Serial.println("✅ I2S播放模块初始化成功");
    return true;
}

bool I2SPlayer::playAudio(const AudioData& audio_data) {
    if (is_playing) {
        Serial.println("⚠️ 正在播放中，跳过新音频");
        return false;
    }

    is_playing = true;
    Serial.printf("🔊 开始播放音频: %d样本\n", audio_data.sample_count);

    // 分块播放音频数据
    size_t chunk_size = DMA_BUF_LEN;
    size_t total_samples = audio_data.sample_count;
    size_t samples_written = 0;

    while (samples_written < total_samples && is_playing) {
        size_t samples_to_write = min(chunk_size, total_samples - samples_written);
        size_t bytes_to_write = samples_to_write * sizeof(int16_t);
        size_t bytes_written = 0;

        esp_err_t err = i2s_write(I2S_NUM_1, 
                                 &audio_data.samples[samples_written],
                                 bytes_to_write, 
                                 &bytes_written, 
                                 portMAX_DELAY);

        if (err != ESP_OK) {
            Serial.printf("❌ I2S写入失败: %s\n", esp_err_to_name(err));
            break;
        }

        samples_written += bytes_written / sizeof(int16_t);
        
        // 让其他任务有机会运行
        vTaskDelay(1);
    }

    // 等待播放完成
    i2s_zero_dma_buffer(I2S_NUM_1);
    
    is_playing = false;
    Serial.println("✅ 音频播放完成");
    
    return true;
}

void I2SPlayer::stopPlaying() {
    if (is_playing) {
        is_playing = false;
        i2s_stop(I2S_NUM_1);
        i2s_zero_dma_buffer(I2S_NUM_1);
        Serial.println("⏹️ 停止播放");
    }
}

bool I2SPlayer::isPlaying() {
    return is_playing;
}

void I2SPlayer::setVolume(float volume) {
    // 音量控制 (0.0 - 1.0)
    this->volume = constrain(volume, 0.0, 1.0);
    Serial.printf("🔊 音量设置为: %.1f%%\n", volume * 100);
}
```

### 5. WebSocket通信模块

```cpp
#include "websocket_client.h"
#include "ArduinoWebsockets.h"
#include "ArduinoJson.h"
#include "base64.h"
#include "config.h"

using namespace websockets;

WebSocketClient::WebSocketClient() : connected(false), reconnect_count(0) {}

bool WebSocketClient::init() {
    // WebSocket事件处理
    client.onMessage([this](WebsocketsMessage message) {
        this->onMessage(message);
    });

    client.onEvent([this](WebsocketsEvent event, String data) {
        this->onEvent(event, data);
    });

    return true;
}

bool WebSocketClient::connect() {
    String url = String("ws://") + SERVER_IP + ":" + SERVER_PORT + WEBSOCKET_PATH;
    
    Serial.printf("🌐 连接WebSocket服务器: %s\n", url.c_str());
    
    bool success = client.connect(url);
    if (success) {
        connected = true;
        reconnect_count = 0;
        Serial.println("✅ WebSocket连接成功");
        
        // 发送连接确认
        sendStatusMessage("connected");
    } else {
        connected = false;
        Serial.println("❌ WebSocket连接失败");
    }
    
    return success;
}

void WebSocketClient::disconnect() {
    if (connected) {
        sendStatusMessage("disconnecting");
        client.close();
        connected = false;
        Serial.println("🔌 WebSocket连接已断开");
    }
}

bool WebSocketClient::sendAudioData(const AudioData& audio_data) {
    if (!connected) {
        Serial.println("❌ WebSocket未连接，无法发送音频");
        return false;
    }

    // 创建JSON消息
    DynamicJsonDocument doc(8192);
    doc["type"] = "audio_input";
    doc["format"] = "pcm";
    doc["sample_rate"] = audio_data.sample_rate;
    doc["channels"] = audio_data.channels;
    doc["bits_per_sample"] = audio_data.bits_per_sample;
    doc["timestamp"] = millis();

    // 将音频数据编码为Base64
    size_t audio_bytes = audio_data.sample_count * sizeof(int16_t);
    String encoded_audio = base64::encode((uint8_t*)audio_data.samples, audio_bytes);
    doc["data"] = encoded_audio;

    // 序列化并发送
    String json_string;
    serializeJson(doc, json_string);
    
    Serial.printf("📤 发送音频数据: %d字节 -> %d字符\n", 
                  audio_bytes, json_string.length());
    
    return client.send(json_string);
}

void WebSocketClient::onMessage(WebsocketsMessage message) {
    Serial.printf("📥 收到消息: %d字节\n", message.length());
    
    // 解析JSON消息
    DynamicJsonDocument doc(8192);
    DeserializationError error = deserializeJson(doc, message.data());
    
    if (error) {
        Serial.printf("❌ JSON解析失败: %s\n", error.c_str());
        return;
    }

    String msg_type = doc["type"];
    
    if (msg_type == "audio_output") {
        handleAudioOutput(doc);
    } else if (msg_type == "status") {
        handleStatusMessage(doc);
    } else if (msg_type == "error") {
        handleErrorMessage(doc);
    } else {
        Serial.printf("⚠️ 未知消息类型: %s\n", msg_type.c_str());
    }
}

void WebSocketClient::handleAudioOutput(JsonDocument& doc) {
    Serial.println("🔊 处理音频输出消息");
    
    String format = doc["format"];
    int sample_rate = doc["sample_rate"];
    String encoded_data = doc["data"];
    
    // 解码Base64音频数据
    String decoded_data = base64::decode(encoded_data);
    
    // 创建音频数据结构
    AudioData audio_data;
    audio_data.sample_count = decoded_data.length() / sizeof(int16_t);
    audio_data.samples = (int16_t*)decoded_data.c_str();
    audio_data.sample_rate = sample_rate;
    audio_data.channels = 1;
    audio_data.bits_per_sample = 16;
    
    // 通知音频播放回调
    if (audio_callback) {
        audio_callback(audio_data);
    }
}

void WebSocketClient::handleStatusMessage(JsonDocument& doc) {
    String command = doc["command"];
    Serial.printf("📋 状态消息: %s\n", command.c_str());
    
    if (status_callback) {
        status_callback(command);
    }
}

void WebSocketClient::handleErrorMessage(JsonDocument& doc) {
    String error_msg = doc["message"];
    Serial.printf("❌ 服务器错误: %s\n", error_msg.c_str());
}

void WebSocketClient::onEvent(WebsocketsEvent event, String data) {
    switch (event) {
        case WebsocketsEvent::ConnectionOpened:
            Serial.println("🔗 WebSocket连接已打开");
            connected = true;
            break;
            
        case WebsocketsEvent::ConnectionClosed:
            Serial.println("🔌 WebSocket连接已关闭");
            connected = false;
            // 自动重连
            scheduleReconnect();
            break;
            
        case WebsocketsEvent::GotPing:
            Serial.println("🏓 收到Ping");
            client.pong();
            break;
            
        case WebsocketsEvent::GotPong:
            Serial.println("🏓 收到Pong");
            break;
    }
}

void WebSocketClient::scheduleReconnect() {
    if (reconnect_count < MAX_RECONNECT_TRIES) {
        reconnect_count++;
        Serial.printf("🔄 计划重连 (%d/%d) 在 %d 秒后\n", 
                      reconnect_count, MAX_RECONNECT_TRIES, RECONNECT_INTERVAL/1000);
        
        // 设置重连定时器
        last_reconnect_attempt = millis();
        should_reconnect = true;
    } else {
        Serial.println("❌ 达到最大重连次数，停止重连");
    }
}

void WebSocketClient::loop() {
    if (connected) {
        client.poll();
    } else if (should_reconnect && 
               (millis() - last_reconnect_attempt) > RECONNECT_INTERVAL) {
        should_reconnect = false;
        connect();
    }
}

bool WebSocketClient::sendStatusMessage(const String& status) {
    if (!connected) return false;
    
    DynamicJsonDocument doc(512);
    doc["type"] = "status";
    doc["status"] = status;
    doc["timestamp"] = millis();
    
    String json_string;
    serializeJson(doc, json_string);
    
    return client.send(json_string);
}
```

## 🖥️ 电脑端集成开发方案

### 1. ESP32接口模块

```python
import asyncio
import websockets
import json
import base64
import logging
from typing import Optional, Callable
import numpy as np
from datetime import datetime

logger = logging.getLogger(__name__)

class ESP32AudioInterface:
    """ESP32音频设备接口"""
    
    def __init__(self, host: str = "0.0.0.0", port: int = 8765):
        self.host = host
        self.port = port
        self.websocket = None
        self.connected_devices = {}
        
        # 回调函数
        self.audio_received_callback: Optional[Callable] = None
        self.device_status_callback: Optional[Callable] = None
        
        # 服务器状态
        self.server = None
        self.running = False
        
    async def start_server(self):
        """启动WebSocket服务器"""
        try:
            self.server = await websockets.serve(
                self.handle_client,
                self.host,
                self.port,
                ping_interval=20,
                ping_timeout=10
            )
            self.running = True
            logger.info(f"🌐 ESP32接口服务器启动: ws://{self.host}:{self.port}")
            
        except Exception as e:
            logger.error(f"❌ 服务器启动失败: {e}")
            raise
    
    async def stop_server(self):
        """停止WebSocket服务器"""
        if self.server:
            self.running = False
            self.server.close()
            await self.server.wait_closed()
            logger.info("🔌 ESP32接口服务器已停止")
    
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        client_ip = websocket.remote_address[0]
        device_id = f"esp32_{client_ip}_{datetime.now().strftime('%H%M%S')}"
        
        logger.info(f"🔗 ESP32设备连接: {client_ip} -> {device_id}")
        self.connected_devices[device_id] = {
            'websocket': websocket,
            'ip': client_ip,
            'connected_at': datetime.now(),
            'last_activity': datetime.now()
        }
        
        try:
            # 发送连接确认
            await self.send_status_message(websocket, "server_ready")
            
            # 处理消息循环
            async for message in websocket:
                await self.process_message(device_id, message)
                
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"🔌 ESP32设备断开: {device_id}")
        except Exception as e:
            logger.error(f"❌ 处理客户端异常: {e}")
        finally:
            # 清理设备记录
            if device_id in self.connected_devices:
                del self.connected_devices[device_id]
    
    async def process_message(self, device_id: str, message: str):
        """处理收到的消息"""
        try:
            data = json.loads(message)
            msg_type = data.get('type')
            
            # 更新设备活动时间
            if device_id in self.connected_devices:
                self.connected_devices[device_id]['last_activity'] = datetime.now()
            
            if msg_type == 'audio_input':
                await self.handle_audio_input(device_id, data)
            elif msg_type == 'status':
                await self.handle_status_message(device_id, data)
            else:
                logger.warning(f"⚠️ 未知消息类型: {msg_type}")
                
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON解析失败: {e}")
        except Exception as e:
            logger.error(f"❌ 消息处理异常: {e}")
    
    async def handle_audio_input(self, device_id: str, data: dict):
        """处理音频输入"""
        try:
            # 解析音频参数
            format_type = data.get('format', 'pcm')
            sample_rate = data.get('sample_rate', 16000)
            channels = data.get('channels', 1)
            bits_per_sample = data.get('bits_per_sample', 16)
            encoded_data = data.get('data', '')
            
            logger.info(f"🎤 收到音频数据: {device_id}, {len(encoded_data)}字符")
            
            # 解码音频数据
            audio_bytes = base64.b64decode(encoded_data)
            audio_array = np.frombuffer(audio_bytes, dtype=np.int16)
            
            # 创建音频信息
            audio_info = {
                'device_id': device_id,
                'format': format_type,
                'sample_rate': sample_rate,
                'channels': channels,
                'bits_per_sample': bits_per_sample,
                'samples': audio_array,
                'timestamp': datetime.now()
            }
            
            # 调用音频处理回调
            if self.audio_received_callback:
                response_audio = await self.audio_received_callback(audio_info)
                if response_audio:
                    await self.send_audio_response(device_id, response_audio)
            
        except Exception as e:
            logger.error(f"❌ 音频处理失败: {e}")
            await self.send_error_message(device_id, f"音频处理失败: {str(e)}")
    
    async def handle_status_message(self, device_id: str, data: dict):
        """处理状态消息"""
        status = data.get('status', 'unknown')
        logger.info(f"📋 设备状态: {device_id} -> {status}")
        
        if self.device_status_callback:
            await self.device_status_callback(device_id, status)
    
    async def send_audio_response(self, device_id: str, audio_data: np.ndarray):
        """发送音频响应到ESP32"""
        try:
            if device_id not in self.connected_devices:
                logger.error(f"❌ 设备未连接: {device_id}")
                return False
            
            websocket = self.connected_devices[device_id]['websocket']
            
            # 编码音频数据
            audio_bytes = audio_data.astype(np.int16).tobytes()
            encoded_audio = base64.b64encode(audio_bytes).decode('utf-8')
            
            # 构建响应消息
            response = {
                'type': 'audio_output',
                'format': 'pcm',
                'sample_rate': 16000,
                'channels': 1,
                'bits_per_sample': 16,
                'data': encoded_audio,
                'timestamp': datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(response))
            logger.info(f"📤 发送音频响应: {device_id}, {len(audio_bytes)}字节")
            return True
            
        except Exception as e:
            logger.error(f"❌ 发送音频响应失败: {e}")
            return False
    
    async def send_status_message(self, websocket, command: str):
        """发送状态消息"""
        try:
            message = {
                'type': 'status',
                'command': command,
                'timestamp': datetime.now().isoformat()
            }
            await websocket.send(json.dumps(message))
            
        except Exception as e:
            logger.error(f"❌ 发送状态消息失败: {e}")
    
    async def send_error_message(self, device_id: str, error_msg: str):
        """发送错误消息"""
        try:
            if device_id not in self.connected_devices:
                return
            
            websocket = self.connected_devices[device_id]['websocket']
            message = {
                'type': 'error',
                'message': error_msg,
                'timestamp': datetime.now().isoformat()
            }
            await websocket.send(json.dumps(message))
            
        except Exception as e:
            logger.error(f"❌ 发送错误消息失败: {e}")
    
    def get_connected_devices(self) -> dict:
        """获取已连接设备列表"""
        return {
            device_id: {
                'ip': info['ip'],
                'connected_at': info['connected_at'].isoformat(),
                'last_activity': info['last_activity'].isoformat()
            }
            for device_id, info in self.connected_devices.items()
        }
    
    def set_audio_callback(self, callback: Callable):
        """设置音频处理回调"""
        self.audio_received_callback = callback
    
    def set_status_callback(self, callback: Callable):
        """设置状态回调"""
        self.device_status_callback = callback
```

### 2. 音频处理集成模块

```python
import asyncio
import numpy as np
import tempfile
import wave
import os
from typing import Optional
import logging

from ..voice.voice_assistant import SpeechRecognizer, TextToSpeech
from ..voice.dialogue_manager import DialogueManager

logger = logging.getLogger(__name__)

class HardwareAudioProcessor:
    """硬件音频处理器 - 集成现有语音系统"""
    
    def __init__(self):
        # 初始化现有组件
        self.speech_recognizer = SpeechRecognizer()
        self.tts = TextToSpeech()
        self.dialogue_manager = DialogueManager()
        
        # 音频处理状态
        self.processing = False
        
        logger.info("🎛️ 硬件音频处理器初始化完成")
    
    async def process_audio_from_esp32(self, audio_info: dict) -> Optional[np.ndarray]:
        """处理来自ESP32的音频数据"""
        try:
            if self.processing:
                logger.warning("⚠️ 正在处理中，跳过新音频")
                return None
            
            self.processing = True
            device_id = audio_info['device_id']
            audio_samples = audio_info['samples']
            sample_rate = audio_info['sample_rate']
            
            logger.info(f"🎤 处理ESP32音频: {device_id}, {len(audio_samples)}样本")
            
            # 1. 保存音频到临时文件
            temp_wav_file = await self._save_audio_to_wav(
                audio_samples, sample_rate
            )
            
            if not temp_wav_file:
                logger.error("❌ 音频文件保存失败")
                return None
            
            # 2. 语音识别
            recognized_text = await self._recognize_speech(temp_wav_file)
            
            # 清理临时文件
            try:
                os.unlink(temp_wav_file)
            except:
                pass
            
            if not recognized_text:
                logger.warning("⚠️ 语音识别无结果")
                return await self._generate_error_response("抱歉，我没有听清楚您说的话")
            
            logger.info(f"🗣️ 识别结果: {recognized_text}")
            
            # 3. 生成对话回复
            response_text = await self._generate_response(recognized_text)
            
            if not response_text:
                logger.warning("⚠️ 对话生成无结果")
                return await self._generate_error_response("抱歉，我暂时无法回答您的问题")
            
            logger.info(f"🤖 生成回复: {response_text}")
            
            # 4. 文本转语音
            response_audio = await self._text_to_speech(response_text)
            
            if response_audio is None:
                logger.error("❌ 语音合成失败")
                return await self._generate_error_response("抱歉，语音合成出现问题")
            
            logger.info(f"🔊 语音合成完成: {len(response_audio)}样本")
            return response_audio
            
        except Exception as e:
            logger.error(f"❌ 音频处理异常: {e}")
            return await self._generate_error_response("抱歉，处理您的请求时出现了问题")
        
        finally:
            self.processing = False
    
    async def _save_audio_to_wav(self, audio_samples: np.ndarray, sample_rate: int) -> Optional[str]:
        """将音频样本保存为WAV文件"""
        try:
            # 创建临时文件
            temp_fd, temp_path = tempfile.mkstemp(suffix='.wav', prefix='esp32_audio_')
            os.close(temp_fd)
            
            # 写入WAV文件
            with wave.open(temp_path, 'wb') as wav_file:
                wav_file.setnchannels(1)  # 单声道
                wav_file.setsampwidth(2)  # 16位
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(audio_samples.astype(np.int16).tobytes())
            
            logger.debug(f"💾 音频已保存: {temp_path}")
            return temp_path
            
        except Exception as e:
            logger.error(f"❌ 保存音频文件失败: {e}")
            return None
    
    async def _recognize_speech(self, wav_file: str) -> Optional[str]:
        """语音识别"""
        try:
            # 使用现有的语音识别系统
            result = await self.speech_recognizer.recognize(wav_file)
            return result
            
        except Exception as e:
            logger.error(f"❌ 语音识别失败: {e}")
            return None
    
    async def _generate_response(self, user_input: str) -> Optional[str]:
        """生成对话回复"""
        try:
            # 添加到对话历史
            self.dialogue_manager.add_message("user", user_input)
            
            # 生成回复 (这里可以集成现有的对话生成逻辑)
            response = await self._simple_response_generator(user_input)
            
            # 添加助手回复到历史
            if response:
                self.dialogue_manager.add_message("assistant", response)
            
            return response
            
        except Exception as e:
            logger.error(f"❌ 对话生成失败: {e}")
            return None
    
    async def _simple_response_generator(self, user_input: str) -> str:
        """简单的回复生成器 (可替换为更复杂的AI系统)"""
        # 获取对话上下文
        context = self.dialogue_manager.get_context()
        
        # 简单的关键词匹配回复
        user_input_lower = user_input.lower()
        
        if any(keyword in user_input_lower for keyword in ["你好", "hello", "hi"]):
            return "您好！我是您的语音助手，有什么可以帮助您的吗？"
        elif any(keyword in user_input_lower for keyword in ["时间", "几点"]):
            from datetime import datetime
            now = datetime.now()
            return f"现在是{now.strftime('%Y年%m月%d日 %H点%M分')}"
        elif any(keyword in user_input_lower for keyword in ["天气"]):
            return "抱歉，我暂时无法查询天气信息，请您稍后再试"
        elif any(keyword in user_input_lower for keyword in ["再见", "拜拜", "退出"]):
            return "再见！很高兴为您服务，期待下次与您对话"
        else:
            return f"我听到您说：{user_input}。作为您的语音助手，我会继续学习以更好地为您服务"
    
    async def _text_to_speech(self, text: str) -> Optional[np.ndarray]:
        """文本转语音"""
        try:
            # 使用现有的TTS系统生成语音
            success = await self.tts.speak(text)
            
            if not success:
                logger.error("❌ TTS生成失败")
                return None
            
            # 获取生成的音频文件
            # 注意：这里需要修改现有TTS系统以返回音频数据而不是直接播放
            audio_file = await self._get_latest_tts_file()
            
            if not audio_file:
                logger.error("❌ 无法获取TTS音频文件")
                return None
            
            # 读取音频文件并转换为numpy数组
            audio_data = await self._load_wav_as_numpy(audio_file)
            
            return audio_data
            
        except Exception as e:
            logger.error(f"❌ 文本转语音失败: {e}")
            return None
    
    async def _get_latest_tts_file(self) -> Optional[str]:
        """获取最新的TTS音频文件"""
        try:
            # 这里需要根据现有TTS系统的实现来获取音频文件
            # 可能需要修改现有的TTS类以支持返回文件路径
            voice_dir = self.tts.voice_dir
            
            # 查找最新的音频文件
            import glob
            audio_files = glob.glob(os.path.join(voice_dir, "tts_*.mp3"))
            
            if not audio_files:
                return None
            
            # 返回最新的文件
            latest_file = max(audio_files, key=os.path.getctime)
            return latest_file
            
        except Exception as e:
            logger.error(f"❌ 获取TTS文件失败: {e}")
            return None
    
    async def _load_wav_as_numpy(self, audio_file: str) -> Optional[np.ndarray]:
        """加载音频文件为numpy数组"""
        try:
            # 如果是MP3文件，需要先转换为WAV
            if audio_file.endswith('.mp3'):
                wav_file = await self._convert_mp3_to_wav(audio_file)
                if not wav_file:
                    return None
                audio_file = wav_file
            
            # 读取WAV文件
            with wave.open(audio_file, 'rb') as wav_file:
                frames = wav_file.readframes(wav_file.getnframes())
                audio_data = np.frombuffer(frames, dtype=np.int16)
                
                # 确保是16kHz单声道
                if wav_file.getframerate() != 16000:
                    # 需要重采样 (这里简化处理)
                    logger.warning("⚠️ 音频采样率不匹配，可能影响播放质量")
                
                return audio_data
                
        except Exception as e:
            logger.error(f"❌ 加载音频文件失败: {e}")
            return None
    
    async def _convert_mp3_to_wav(self, mp3_file: str) -> Optional[str]:
        """将MP3转换为WAV"""
        try:
            import subprocess
            
            # 生成WAV文件路径
            wav_file = mp3_file.replace('.mp3', '.wav')
            
            # 使用ffmpeg转换
            cmd = [
                'ffmpeg', '-i', mp3_file,
                '-ar', '16000',  # 16kHz采样率
                '-ac', '1',      # 单声道
                '-y',            # 覆盖输出文件
                wav_file
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return wav_file
            else:
                logger.error(f"❌ MP3转WAV失败: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"❌ MP3转换异常: {e}")
            return None
    
    async def _generate_error_response(self, error_message: str) -> np.ndarray:
        """生成错误响应音频"""
        try:
            # 生成简单的错误提示音频
            # 这里可以预先录制一些错误提示音频
            error_audio = await self._text_to_speech(error_message)
            
            if error_audio is not None:
                return error_audio
            
            # 如果TTS失败，生成静音
            silence_duration = 2  # 2秒静音
            silence_samples = int(16000 * silence_duration)
            return np.zeros(silence_samples, dtype=np.int16)
            
        except Exception as e:
            logger.error(f"❌ 生成错误响应失败: {e}")
            # 返回短暂静音
            return np.zeros(16000, dtype=np.int16)
```

## 🔄 音频转换流程详解

### 1. 完整音频处理流程图

```mermaid
graph TD
    subgraph "ESP32端音频处理"
        A1[用户说话] --> A2[INMP441麦克风采集]
        A2 --> A3[I2S接口读取PCM数据]
        A3 --> A4[语音活动检测VAD]
        A4 --> A5{检测到语音?}
        A5 -->|是| A6[开始录音缓冲]
        A5 -->|否| A4
        A6 --> A7[静音检测]
        A7 --> A8{静音超时?}
        A8 -->|否| A6
        A8 -->|是| A9[停止录音]
        A9 --> A10[PCM数据Base64编码]
        A10 --> A11[WebSocket发送到电脑]
    end
    
    subgraph "电脑端音频处理"
        B1[接收WebSocket消息] --> B2[JSON解析]
        B2 --> B3[Base64解码PCM数据]
        B3 --> B4[转换为numpy数组]
        B4 --> B5[保存为临时WAV文件]
        B5 --> B6[FunASR语音识别]
        B6 --> B7[对话管理生成回复]
        B7 --> B8[Edge-TTS语音合成]
        B8 --> B9[MP3转WAV格式]
        B9 --> B10[读取为numpy数组]
        B10 --> B11[Base64编码]
        B11 --> B12[WebSocket发送到ESP32]
    end
    
    subgraph "ESP32端音频播放"
        C1[接收WebSocket消息] --> C2[JSON解析]
        C2 --> C3[Base64解码PCM数据]
        C3 --> C4[写入I2S播放缓冲区]
        C4 --> C5[MAX98357A功放输出]
        C5 --> C6[扬声器播放声音]
    end
    
    A11 --> B1
    B12 --> C1
```

### 2. 音频格式转换详细流程

#### ESP32端录音转换流程
```mermaid
graph LR
    subgraph "硬件层"
        H1[声波] --> H2[INMP441麦克风]
        H2 --> H3[模拟信号]
        H3 --> H4[ADC转换]
        H4 --> H5[数字PCM信号]
    end
    
    subgraph "ESP32处理层"
        E1[I2S DMA接收] --> E2[16位PCM数据]
        E2 --> E3[音频缓冲区]
        E3 --> E4[VAD语音检测]
        E4 --> E5[数据打包]
        E5 --> E6[Base64编码]
        E6 --> E7[JSON封装]
        E7 --> E8[WebSocket传输]
    end
    
    H5 --> E1
```

#### 电脑端音频处理流程
```mermaid
graph TB
    subgraph "接收处理"
        R1[WebSocket接收] --> R2[JSON解析]
        R2 --> R3[Base64解码]
        R3 --> R4[PCM字节数组]
        R4 --> R5[numpy.int16数组]
        R5 --> R6[WAV文件保存]
    end
    
    subgraph "语音识别"
        S1[WAV文件] --> S2[FunASR处理]
        S2 --> S3[语音转文本]
        S3 --> S4[文本结果]
    end
    
    subgraph "对话生成"
        D1[用户文本] --> D2[对话管理器]
        D2 --> D3[上下文分析]
        D3 --> D4[回复生成]
        D4 --> D5[回复文本]
    end
    
    subgraph "语音合成"
        T1[回复文本] --> T2[Edge-TTS]
        T2 --> T3[MP3音频文件]
        T3 --> T4[ffmpeg转WAV]
        T4 --> T5[16kHz PCM数据]
        T5 --> T6[numpy数组]
        T6 --> T7[Base64编码]
        T7 --> T8[WebSocket发送]
    end
    
    R6 --> S1
    S4 --> D1
    D5 --> T1
```

### 3. 主程序入口文件

```cpp
#include <Arduino.h>
#include <WiFi.h>
#include <ArduinoWebsockets.h>
#include <ArduinoJson.h>

// 包含自定义模块
#include "audio/i2s_recorder.h"
#include "audio/i2s_player.h"
#include "network/websocket_client.h"
#include "control/led_controller.h"
#include "control/button_handler.h"
#include "utils/config.h"

// 全局对象
I2SRecorder recorder;
I2SPlayer player;
WebSocketClient wsClient;
LEDController ledController;
ButtonHandler buttonHandler;

// 系统状态
enum SystemState {
    SYSTEM_INIT,
    SYSTEM_CONNECTING,
    SYSTEM_READY,
    SYSTEM_LISTENING,
    SYSTEM_PROCESSING,
    SYSTEM_SPEAKING,
    SYSTEM_ERROR
};

SystemState currentState = SYSTEM_INIT;
unsigned long lastHeartbeat = 0;
unsigned long stateChangeTime = 0;

// 任务句柄
TaskHandle_t audioTaskHandle = NULL;
TaskHandle_t networkTaskHandle = NULL;
TaskHandle_t controlTaskHandle = NULL;

void setup() {
    Serial.begin(115200);
    Serial.println("\n🚀 ESP32语音助手启动中...");
    
    // 初始化LED控制器
    ledController.init();
    ledController.setState(LED_STATE_BOOTING);
    
    // 初始化按键处理器
    buttonHandler.init();
    buttonHandler.setCallback(onButtonPressed);
    
    // 初始化音频模块
    if (!recorder.init()) {
        Serial.println("❌ 录音模块初始化失败");
        setState(SYSTEM_ERROR);
        return;
    }
    
    if (!player.init()) {
        Serial.println("❌ 播放模块初始化失败");
        setState(SYSTEM_ERROR);
        return;
    }
    
    // 初始化WebSocket客户端
    wsClient.init();
    wsClient.setAudioCallback(onAudioReceived);
    wsClient.setStatusCallback(onStatusReceived);
    
    // 连接WiFi
    connectToWiFi();
    
    // 创建任务
    createTasks();
    
    Serial.println("✅ ESP32语音助手初始化完成");
    setState(SYSTEM_READY);
}

void loop() {
    // 主循环保持简洁，主要工作由任务处理
    vTaskDelay(pdMS_TO_TICKS(100));
    
    // 定期发送心跳
    if (millis() - lastHeartbeat > 30000) {  // 30秒心跳
        sendHeartbeat();
        lastHeartbeat = millis();
    }
    
    // 监控系统状态
    monitorSystemHealth();
}

void connectToWiFi() {
    Serial.printf("🌐 连接WiFi: %s\n", WIFI_SSID);
    setState(SYSTEM_CONNECTING);
    
    WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
    
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 30) {
        delay(1000);
        Serial.print(".");
        attempts++;
        
        // 更新LED状态
        ledController.setState(LED_STATE_CONNECTING);
    }
    
    if (WiFi.status() == WL_CONNECTED) {
        Serial.printf("\n✅ WiFi连接成功: %s\n", WiFi.localIP().toString().c_str());
        
        // 连接WebSocket服务器
        if (wsClient.connect()) {
            setState(SYSTEM_READY);
        } else {
            setState(SYSTEM_ERROR);
        }
    } else {
        Serial.println("\n❌ WiFi连接失败");
        setState(SYSTEM_ERROR);
    }
}

void createTasks() {
    // 音频处理任务
    xTaskCreatePinnedToCore(
        audioTask,
        "AudioTask",
        8192,  // 栈大小
        NULL,
        2,     // 优先级
        &audioTaskHandle,
        1      // CPU核心
    );
    
    // 网络通信任务
    xTaskCreatePinnedToCore(
        networkTask,
        "NetworkTask",
        4096,
        NULL,
        1,
        &networkTaskHandle,
        0
    );
    
    // 控制任务
    xTaskCreatePinnedToCore(
        controlTask,
        "ControlTask",
        2048,
        NULL,
        1,
        &controlTaskHandle,
        0
    );
}

void audioTask(void* parameter) {
    Serial.println("🎵 音频任务启动");
    
    const size_t bufferSize = 1024;
    int16_t audioBuffer[bufferSize];
    
    while (true) {
        if (currentState == SYSTEM_LISTENING) {
            // 读取音频数据
            size_t samplesRead = recorder.readAudio(audioBuffer, bufferSize);
            
            if (samplesRead > 0) {
                // 检查是否静音超时
                if (recorder.isSilenceTimeout()) {
                    Serial.println("🔇 检测到静音，停止录音");
                    
                    // 获取完整录音数据
                    AudioData recordedAudio = recorder.getRecordedAudio();
                    
                    // 发送音频数据
                    if (recordedAudio.sample_count > 0) {
                        wsClient.sendAudioData(recordedAudio);
                        setState(SYSTEM_PROCESSING);
                    }
                    
                    recorder.stopRecording();
                }
            }
        }
        
        vTaskDelay(pdMS_TO_TICKS(10));  // 10ms延迟
    }
}

void networkTask(void* parameter) {
    Serial.println("🌐 网络任务启动");
    
    while (true) {
        // 处理WebSocket消息
        wsClient.loop();
        
        // 检查网络连接状态
        if (WiFi.status() != WL_CONNECTED) {
            Serial.println("⚠️ WiFi连接丢失，尝试重连");
            setState(SYSTEM_CONNECTING);
            connectToWiFi();
        }
        
        vTaskDelay(pdMS_TO_TICKS(50));  // 50ms延迟
    }
}

void controlTask(void* parameter) {
    Serial.println("🎛️ 控制任务启动");
    
    while (true) {
        // 处理按键输入
        buttonHandler.loop();
        
        // 更新LED状态
        updateLEDStatus();
        
        // 系统状态管理
        handleStateTransitions();
        
        vTaskDelay(pdMS_TO_TICKS(20));  // 20ms延迟
    }
}

void setState(SystemState newState) {
    if (currentState != newState) {
        Serial.printf("🔄 状态变更: %s -> %s\n", 
                      getStateName(currentState), 
                      getStateName(newState));
        
        currentState = newState;
        stateChangeTime = millis();
        
        // 更新LED状态
        updateLEDStatus();
    }
}

const char* getStateName(SystemState state) {
    switch (state) {
        case SYSTEM_INIT: return "初始化";
        case SYSTEM_CONNECTING: return "连接中";
        case SYSTEM_READY: return "就绪";
        case SYSTEM_LISTENING: return "监听中";
        case SYSTEM_PROCESSING: return "处理中";
        case SYSTEM_SPEAKING: return "播放中";
        case SYSTEM_ERROR: return "错误";
        default: return "未知";
    }
}

void updateLEDStatus() {
    switch (currentState) {
        case SYSTEM_INIT:
            ledController.setState(LED_STATE_BOOTING);
            break;
        case SYSTEM_CONNECTING:
            ledController.setState(LED_STATE_CONNECTING);
            break;
        case SYSTEM_READY:
            ledController.setState(LED_STATE_READY);
            break;
        case SYSTEM_LISTENING:
            ledController.setState(LED_STATE_LISTENING);
            break;
        case SYSTEM_PROCESSING:
            ledController.setState(LED_STATE_PROCESSING);
            break;
        case SYSTEM_SPEAKING:
            ledController.setState(LED_STATE_SPEAKING);
            break;
        case SYSTEM_ERROR:
            ledController.setState(LED_STATE_ERROR);
            break;
    }
}

void handleStateTransitions() {
    unsigned long stateTime = millis() - stateChangeTime;
    
    switch (currentState) {
        case SYSTEM_READY:
            // 自动开始监听（可选）
            if (stateTime > 2000) {  // 就绪2秒后开始监听
                startListening();
            }
            break;
            
        case SYSTEM_PROCESSING:
            // 处理超时检查
            if (stateTime > 30000) {  // 30秒超时
                Serial.println("⏰ 处理超时，返回就绪状态");
                setState(SYSTEM_READY);
            }
            break;
            
        case SYSTEM_ERROR:
            // 错误状态自动恢复
            if (stateTime > 10000) {  // 10秒后尝试恢复
                Serial.println("🔄 尝试从错误状态恢复");
                setState(SYSTEM_INIT);
                ESP.restart();  // 重启系统
            }
            break;
    }
}

void startListening() {
    if (currentState == SYSTEM_READY) {
        Serial.println("🎤 开始监听语音...");
        
        if (recorder.startRecording()) {
            setState(SYSTEM_LISTENING);
        } else {
            Serial.println("❌ 启动录音失败");
            setState(SYSTEM_ERROR);
        }
    }
}

void onButtonPressed() {
    Serial.println("🔘 按键按下");
    
    switch (currentState) {
        case SYSTEM_READY:
            startListening();
            break;
            
        case SYSTEM_LISTENING:
            // 手动停止录音
            recorder.stopRecording();
            AudioData recordedAudio = recorder.getRecordedAudio();
            if (recordedAudio.sample_count > 0) {
                wsClient.sendAudioData(recordedAudio);
                setState(SYSTEM_PROCESSING);
            } else {
                setState(SYSTEM_READY);
            }
            break;
            
        case SYSTEM_SPEAKING:
            // 中断播放
            player.stopPlaying();
            setState(SYSTEM_READY);
            break;
    }
}

void onAudioReceived(const AudioData& audioData) {
    Serial.printf("🔊 收到音频响应: %d样本\n", audioData.sample_count);
    
    setState(SYSTEM_SPEAKING);
    
    // 播放音频
    if (player.playAudio(audioData)) {
        Serial.println("✅ 音频播放完成");
    } else {
        Serial.println("❌ 音频播放失败");
    }
    
    setState(SYSTEM_READY);
}

void onStatusReceived(const String& status) {
    Serial.printf("📋 收到状态消息: %s\n", status.c_str());
    
    if (status == "server_ready") {
        Serial.println("✅ 服务器就绪");
        setState(SYSTEM_READY);
    } else if (status == "processing") {
        setState(SYSTEM_PROCESSING);
    } else if (status == "error") {
        setState(SYSTEM_ERROR);
    }
}

void sendHeartbeat() {
    if (wsClient.isConnected()) {
        wsClient.sendStatusMessage("heartbeat");
    }
}

void monitorSystemHealth() {
    // 监控内存使用
    size_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < 10000) {  // 少于10KB
        Serial.printf("⚠️ 内存不足: %d字节\n", freeHeap);
    }
    
    // 监控任务状态
    if (audioTaskHandle && eTaskGetState(audioTaskHandle) == eDeleted) {
        Serial.println("❌ 音频任务异常终止");
        setState(SYSTEM_ERROR);
    }
    
    if (networkTaskHandle && eTaskGetState(networkTaskHandle) == eDeleted) {
        Serial.println("❌ 网络任务异常终止");
        setState(SYSTEM_ERROR);
    }
}
```

### 4. LED状态控制模块

```cpp
#include "led_controller.h"
#include "config.h"

LEDController::LEDController() : currentState(LED_STATE_OFF), lastUpdate(0) {}

bool LEDController::init() {
    pinMode(LED_PIN, OUTPUT);
    digitalWrite(LED_PIN, LOW);
    
    Serial.println("✅ LED控制器初始化完成");
    return true;
}

void LEDController::setState(LEDState state) {
    if (currentState != state) {
        currentState = state;
        lastUpdate = millis();
        Serial.printf("💡 LED状态: %s\n", getStateName(state));
    }
}

void LEDController::loop() {
    unsigned long now = millis();
    
    switch (currentState) {
        case LED_STATE_OFF:
            digitalWrite(LED_PIN, LOW);
            break;
            
        case LED_STATE_BOOTING:
            // 快速闪烁
            digitalWrite(LED_PIN, (now / 200) % 2);
            break;
            
        case LED_STATE_CONNECTING:
            // 慢速闪烁
            digitalWrite(LED_PIN, (now / 1000) % 2);
            break;
            
        case LED_STATE_READY:
            // 常亮
            digitalWrite(LED_PIN, HIGH);
            break;
            
        case LED_STATE_LISTENING:
            // 呼吸灯效果
            breathingEffect(now);
            break;
            
        case LED_STATE_PROCESSING:
            // 快速闪烁
            digitalWrite(LED_PIN, (now / 100) % 2);
            break;
            
        case LED_STATE_SPEAKING:
            // 中速闪烁
            digitalWrite(LED_PIN, (now / 300) % 2);
            break;
            
        case LED_STATE_ERROR:
            // SOS信号
            sosPattern(now);
            break;
    }
}

void LEDController::breathingEffect(unsigned long now) {
    // 简化的呼吸灯效果（需要PWM支持）
    int brightness = (sin((now % 2000) * 2 * PI / 2000) + 1) * 127;
    analogWrite(LED_PIN, brightness);
}

void LEDController::sosPattern(unsigned long now) {
    // SOS: ... --- ...
    unsigned long pattern = now % 4000;  // 4秒周期
    
    if (pattern < 600) {
        // 三个短闪 (...)
        digitalWrite(LED_PIN, (pattern / 100) % 2);
    } else if (pattern < 1200) {
        digitalWrite(LED_PIN, LOW);
    } else if (pattern < 2400) {
        // 三个长闪 (---)
        digitalWrite(LED_PIN, (pattern / 400) % 2);
    } else if (pattern < 3000) {
        digitalWrite(LED_PIN, LOW);
    } else if (pattern < 3600) {
        // 三个短闪 (...)
        digitalWrite(LED_PIN, ((pattern - 3000) / 100) % 2);
    } else {
        digitalWrite(LED_PIN, LOW);
    }
}

const char* LEDController::getStateName(LEDState state) {
    switch (state) {
        case LED_STATE_OFF: return "关闭";
        case LED_STATE_BOOTING: return "启动中";
        case LED_STATE_CONNECTING: return "连接中";
        case LED_STATE_READY: return "就绪";
        case LED_STATE_LISTENING: return "监听中";
        case LED_STATE_PROCESSING: return "处理中";
        case LED_STATE_SPEAKING: return "播放中";
        case LED_STATE_ERROR: return "错误";
        default: return "未知";
    }
}
```

## 📱 电脑端完整集成方案

### 1. 启动脚本集成

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32硬件语音助手启动器
集成到现有的AI安全监控系统中
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime

# 导入现有系统模块
from src.voice.voice_assistant import VoiceConfig
from src.voice.start_enhanced_voice_assistant import EnhancedVoiceAssistant

# 导入新增的ESP32接口模块
from src.hardware.esp32_audio_interface import ESP32AudioInterface
from src.hardware.hardware_audio_processor import HardwareAudioProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ESP32VoiceAssistantManager:
    """ESP32语音助手管理器"""
    
    def __init__(self):
        self.esp32_interface = None
        self.audio_processor = None
        self.enhanced_assistant = None
        self.running = False
        
    async def start(self):
        """启动ESP32语音助手系统"""
        try:
            logger.info("🚀 启动ESP32语音助手系统...")
            
            # 1. 初始化音频处理器
            self.audio_processor = HardwareAudioProcessor()
            logger.info("✅ 音频处理器初始化完成")
            
            # 2. 初始化ESP32接口
            self.esp32_interface = ESP32AudioInterface(
                host="0.0.0.0",  # 监听所有接口
                port=8765        # WebSocket端口
            )
            
            # 设置音频处理回调
            self.esp32_interface.set_audio_callback(
                self.audio_processor.process_audio_from_esp32
            )
            self.esp32_interface.set_status_callback(
                self.handle_device_status
            )
            
            # 3. 启动WebSocket服务器
            await self.esp32_interface.start_server()
            logger.info("✅ ESP32接口服务器启动完成")
            
            # 4. 启动增强语音助手（可选，用于电脑端语音）
            if VoiceConfig.ENABLE_COMPUTER_VOICE:
                self.enhanced_assistant = EnhancedVoiceAssistant()
                await self.enhanced_assistant.start()
                logger.info("✅ 电脑端语音助手启动完成")
            
            self.running = True
            logger.info("🎉 ESP32语音助手系统启动成功！")
            
            # 显示连接信息
            self.print_connection_info()
            
            # 主循环
            await self.main_loop()
            
        except Exception as e:
            logger.error(f"❌ 系统启动失败: {e}")
            raise
    
    async def stop(self):
        """停止ESP32语音助手系统"""
        logger.info("🛑 正在停止ESP32语音助手系统...")
        
        self.running = False
        
        try:
            # 停止增强语音助手
            if self.enhanced_assistant:
                await self.enhanced_assistant.stop()
                logger.info("✅ 电脑端语音助手已停止")
            
            # 停止ESP32接口服务器
            if self.esp32_interface:
                await self.esp32_interface.stop_server()
                logger.info("✅ ESP32接口服务器已停止")
            
            logger.info("✅ ESP32语音助手系统已完全停止")
            
        except Exception as e:
            logger.error(f"❌ 停止系统时出错: {e}")
    
    async def main_loop(self):
        """主循环"""
        logger.info("🔄 进入主循环...")
        
        try:
            while self.running:
                # 定期检查系统状态
                await self.check_system_health()
                
                # 显示连接设备状态
                await self.display_device_status()
                
                # 等待一段时间
                await asyncio.sleep(30)  # 30秒检查一次
                
        except KeyboardInterrupt:
            logger.info("收到中断信号")
        except Exception as e:
            logger.error(f"主循环异常: {e}")
    
    async def handle_device_status(self, device_id: str, status: str):
        """处理设备状态变化"""
        logger.info(f"📱 设备状态更新: {device_id} -> {status}")
        
        # 可以在这里添加设备状态处理逻辑
        # 例如：记录设备活动、发送通知等
    
    async def check_system_health(self):
        """检查系统健康状态"""
        try:
            # 检查ESP32接口状态
            if self.esp32_interface and self.esp32_interface.running:
                connected_count = len(self.esp32_interface.get_connected_devices())
                if connected_count > 0:
                    logger.debug(f"💚 系统健康: {connected_count}个ESP32设备已连接")
                else:
                    logger.debug("💛 系统运行中，等待ESP32设备连接...")
            else:
                logger.warning("💔 ESP32接口服务器未运行")
            
        except Exception as e:
            logger.error(f"❌ 系统健康检查失败: {e}")
    
    async def display_device_status(self):
        """显示设备状态"""
        if not self.esp32_interface:
            return
        
        devices = self.esp32_interface.get_connected_devices()
        
        if devices:
            logger.info(f"📱 已连接设备数量: {len(devices)}")
            for device_id, info in devices.items():
                logger.info(f"  - {device_id}: {info['ip']} (连接时间: {info['connected_at']})")
        else:
            logger.debug("📱 当前无ESP32设备连接")
    
    def print_connection_info(self):
        """打印连接信息"""
        print("\n" + "="*60)
        print("🎤 ESP32硬件语音助手系统已启动")
        print("="*60)
        print(f"📡 WebSocket服务器: ws://0.0.0.0:8765")
        print(f"🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("\n📋 ESP32设备连接步骤:")
        print("1. 确保ESP32和电脑在同一网络")
        print("2. 修改ESP32代码中的SERVER_IP为本机IP")
        print("3. 烧录代码到ESP32设备")
        print("4. 观察串口输出确认连接成功")
        print("\n💡 使用说明:")
        print("- 对ESP32设备说话进行语音交互")
        print("- 观察LED指示灯了解设备状态")
        print("- 按Ctrl+C退出系统")
        print("="*60 + "\n")

async def main():
    """主函数"""
    # 创建管理器
    manager = ESP32VoiceAssistantManager()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        logger.info("收到退出信号，正在关闭系统...")
        asyncio.create_task(manager.stop())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 启动系统
        await manager.start()
        
    except KeyboardInterrupt:
        logger.info("用户中断")
    except Exception as e:
        logger.error(f"系统异常: {e}")
    finally:
        # 确保清理资源
        await manager.stop()

if __name__ == "__main__":
    # 运行主程序
    asyncio.run(main())
```

### 2. 配置文件更新

```python
# src/hardware/esp32_config.py
"""ESP32硬件配置"""

class ESP32Config:
    """ESP32硬件配置类"""
    
    # WebSocket服务器配置
    WEBSOCKET_HOST = "0.0.0.0"
    WEBSOCKET_PORT = 8765
    WEBSOCKET_PATH = "/voice"
    
    # 音频格式配置
    AUDIO_SAMPLE_RATE = 16000
    AUDIO_CHANNELS = 1
    AUDIO_BITS_PER_SAMPLE = 16
    AUDIO_FORMAT = "pcm"
    
    # 设备管理配置
    MAX_CONNECTED_DEVICES = 10
    DEVICE_TIMEOUT = 300  # 5分钟无活动超时
    HEARTBEAT_INTERVAL = 30  # 30秒心跳间隔
    
    # 音频处理配置
    MAX_AUDIO_DURATION = 30  # 最大录音时长(秒)
    MIN_AUDIO_DURATION = 1   # 最小录音时长(秒)
    AUDIO_BUFFER_SIZE = 8192 # 音频缓冲区大小
    
    # 错误处理配置
    MAX_RETRY_ATTEMPTS = 3
    RETRY_DELAY = 1.0  # 重试延迟(秒)
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 性能监控配置
    ENABLE_PERFORMANCE_MONITORING = True
    PERFORMANCE_LOG_INTERVAL = 60  # 性能日志间隔(秒)
```

## 🔧 部署和测试指南

### 1. 硬件组装检查清单

```markdown
## 硬件组装检查清单

### 📦 硬件准备
- [ ] ESP32开发板 x1
- [ ] INMP441麦克风模块 x1  
- [ ] MAX98357A功放模块 x1
- [ ] 4Ω 3W扬声器 x1
- [ ] 杜邦线若干
- [ ] 面包板 x1
- [ ] LED指示灯 x1 (可选)
- [ ] 按键开关 x1 (可选)

### 🔌 连接检查
- [ ] ESP32 GPIO32 → INMP441 WS
- [ ] ESP32 GPIO33 → INMP441 SCK  
- [ ] ESP32 GPIO34 → INMP441 SD
- [ ] ESP32 GPIO21 → MAX98357A LRC
- [ ] ESP32 GPIO22 → MAX98357A BCLK
- [ ] ESP32 GPIO25 → MAX98357A DIN
- [ ] ESP32 3.3V → 所有模块VCC
- [ ] ESP32 GND → 所有模块GND
- [ ] MAX98357A → 扬声器连接

### ⚡ 供电检查
- [ ] ESP32供电正常(USB或外部电源)
- [ ] 各模块供电指示灯亮起
- [ ] 无短路现象
- [ ] 连接牢固可靠
```

### 2. 软件部署步骤

```bash
# 1. 电脑端环境准备
cd /path/to/your/project

# 安装新增依赖
pip install websockets asyncio numpy

# 2. ESP32开发环境准备
# 安装PlatformIO
pip install platformio

# 创建ESP32项目
pio project init --board esp32dev

# 安装必要库
pio lib install "ArduinoWebSockets"
pio lib install "ArduinoJson"
pio lib install "ESP32-audioI2S"

# 3. 代码部署
# 将ESP32代码复制到src目录
# 修改config.h中的WiFi和服务器配置

# 4. 编译和烧录
pio run --target upload --target monitor
```

### 3. 测试流程

```python
#!/usr/bin/env python3
"""ESP32语音助手测试脚本"""

import asyncio
import logging
from src.hardware.esp32_audio_interface import ESP32AudioInterface

async def test_esp32_connection():
    """测试ESP32连接"""
    print("🧪 开始ESP32连接测试...")
    
    interface = ESP32AudioInterface(host="0.0.0.0", port=8765)
    
    # 设置测试回调
    async def test_audio_callback(audio_info):
        print(f"✅ 收到音频: {len(audio_info['samples'])}样本")
        # 返回测试音频响应
        import numpy as np
        test_response = np.sin(2 * np.pi * 440 * np.arange(16000) / 16000)
        return (test_response * 32767).astype(np.int16)
    
    async def test_status_callback(device_id, status):
        print(f"📱 设备状态: {device_id} -> {status}")
    
    interface.set_audio_callback(test_audio_callback)
    interface.set_status_callback(test_status_callback)
    
    try:
        await interface.start_server()
        print("🌐 测试服务器启动成功，等待ESP32连接...")
        print("请启动ESP32设备并观察连接状态")
        
        # 运行测试
        await asyncio.sleep(300)  # 运行5分钟测试
        
    except KeyboardInterrupt:
        print("🛑 测试中断")
    finally:
        await interface.stop_server()
        print("✅ 测试完成")

if __name__ == "__main__":
    asyncio.run(test_esp32_connection())
```

## 📊 性能优化建议

### 1. 音频质量优化
- 使用合适的采样率(16kHz)
- 实现音频降噪算法
- 优化I2S DMA缓冲区大小
- 添加音频压缩传输

### 2. 网络延迟优化  
- 使用有线网络连接
- 优化WebSocket消息格式
- 实现音频流式传输
- 添加本地音频缓存

### 3. 系统稳定性优化
- 实现看门狗重启机制
- 添加内存泄漏检测
- 优化任务调度策略
- 实现故障自动恢复

### 4. 用户体验优化
- 优化语音唤醒灵敏度
- 添加音量自动调节
- 实现多设备管理
- 添加语音指令扩展

## 🎯 总结

这个ESP32硬件实时语音问答开发方案提供了完整的技术实现路径，从硬件连接到软件开发，从音频处理到网络通信，涵盖了项目开发的各个方面。

### 主要特点：
- **硬件成本低**：总成本不超过100元
- **开发难度适中**：适合有一定编程基础的开发者
- **扩展性强**：可以轻松添加新功能
- **集成度高**：与现有系统无缝集成

### 预期效果：
- 实现真正的硬件语音交互
- 响应速度快，延迟低于3秒
- 音质清晰，识别准确率高
- 系统稳定，支持长时间运行

通过这个方案，可以将现有的电脑端语音问答系统扩展为真正的智能硬件设备，为用户提供更加便捷和自然的语音交互体验。
